{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.19.0/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/android/app/.cxx/Debug/6q6pn644/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/android/app/.cxx/Debug/6q6pn644/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}