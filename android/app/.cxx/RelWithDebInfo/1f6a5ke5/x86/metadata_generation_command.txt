                        -H/opt/homebrew/Caskroom/flutter/3.19.0/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/build/app/intermediates/cxx/RelWithDebInfo/1f6a5ke5/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/build/app/intermediates/cxx/RelWithDebInfo/1f6a5ke5/obj/x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/android/app/.cxx/RelWithDebInfo/1f6a5ke5/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2