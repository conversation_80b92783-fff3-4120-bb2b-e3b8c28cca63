{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.19.0/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/android/app/.cxx/RelWithDebInfo/1f6a5ke5/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/AndroidStudioProjects/external-proj/bla_application/android/app/.cxx/RelWithDebInfo/1f6a5ke5/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}