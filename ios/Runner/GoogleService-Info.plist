
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>347435169642-acqlrnevdkteq5bu7vckmjbv1onjn9k2.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.347435169642-acqlrnevdkteq5bu7vckmjbv1onjn9k2</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>347435169642-g1qs2m2qr3t902hd2f2mb4p0h4vk64k0.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyAoABdX2pQjfVI3KT1Un5m5XWAZR4RiKzk</string>
	<key>GCM_SENDER_ID</key>
	<string>347435169642</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>iq.bla.app</string>
	<key>PROJECT_ID</key>
	<string>bla-translate-cf63e</string>
	<key>STORAGE_BUCKET</key>
	<string>bla-translate-cf63e.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:347435169642:ios:462fda67cf457d48e09713</string>
</dict>
</plist>