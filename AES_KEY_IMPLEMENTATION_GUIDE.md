# AES Key Implementation Guide

## Overview

This guide explains the implementation of separate AES keys for different route types in your Flutter application. The system now uses:

- **AUTH_AES_KEY**: For authentication routes (login, signup, forgot password, etc.)
- **TRANSLATION_AES_KEY**: For main app features (translation, dictionary, speech-to-text, etc.)

## Changes Made

### 1. Updated AESEncryptionManager

**File**: `lib/src/core/utils/managers/security/aes_encryption_manager.dart`

- Added `AESKeyType` enum with `auth` and `translation` values
- Modified all encryption/decryption methods to accept an optional `keyType` parameter
- Updated implementation to support both keys simultaneously
- Default behavior uses `AESKeyType.auth` for backward compatibility

### 2. Updated Data Sources

#### Authentication Routes (using AUTH_AES_KEY):
- `lib/src/features/auth/data/data_sources/remote/auth_remote_datasources.dart`
  - login()
  - signup()
  - socialLogin()
  - requestForgotPassword()
  - verifyOtp()
  - resetPassword()
  - requestRegistrationOtp()
  - checkAlreadyExist()

#### Translation/Main App Routes (using TRANSLATION_AES_KEY):
- `lib/src/features/translation/data/datasources/remote/translation_remote_datasource.dart`
- `lib/src/features/translation/data/datasources/remote/text_to_speech_remote_datasource.dart`
- `lib/src/features/translation/data/datasources/remote/speech_to_text_remote_datasource.dart`
- `lib/src/features/dictionary/data/datasources/remote/dictionary_remote_data_source.dart`
- `lib/src/features/document/data/datasources/remote/document_translation_remote_datasource.dart`

### 3. Created Helper Utility

**File**: `lib/src/core/utils/helpers/encryption_helper.dart`

Provides convenient methods:
- `encryptAuthPayload()` / `decryptAuthData()` for auth routes
- `encryptTranslationPayload()` / `decryptTranslationData()` for main app routes

## Environment Variables Required

Make sure your `.env` file contains both keys:

```env
AUTH_AES_KEY=your_auth_aes_key_here
TRANSLATION_AES_KEY=your_translation_aes_key_here
AES_IV=your_aes_iv_here
```

## Usage Examples

### For Authentication Routes

```dart
// Encryption
final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
  loginPayload.toJson(),
  keyType: AESKeyType.auth,
);

// Decryption
final String decryptedData = await aesEncryptionManager.decryptData(
  responseData['data'],
  keyType: AESKeyType.auth,
);
```

### For Translation/Main App Routes

```dart
// Encryption
final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
  translationPayload.toJson(),
  keyType: AESKeyType.translation,
);

// Decryption
final String decryptedData = await aesEncryptionManager.decryptData(
  responseData['data'],
  keyType: AESKeyType.translation,
);
```

### Using the Helper Class

```dart
final encryptionHelper = EncryptionHelper(aesEncryptionManager);

// For auth routes
final authEncrypted = await encryptionHelper.encryptAuthPayload(payload);
final authDecrypted = await encryptionHelper.decryptAuthData(encryptedData);

// For translation routes
final translationEncrypted = await encryptionHelper.encryptTranslationPayload(payload);
final translationDecrypted = await encryptionHelper.decryptTranslationData(encryptedData);
```

## Route Classification

### Authentication Routes (use AUTH_AES_KEY):
- `/login`
- `/signup`
- `/forgot-password`
- `/verify-otp`
- `/reset-password`
- `/check-already-exist`
- `/registration-otp`
- Social login endpoints

### Main App Routes (use TRANSLATION_AES_KEY):
- `/translate`
- `/text-to-speech`
- `/speech-to-text`
- `/dictionary`
- `/document-translation`
- Any other main app functionality

## Benefits

1. **Security**: Different keys for different functionalities
2. **Separation of Concerns**: Auth and main app features use separate encryption
3. **Flexibility**: Easy to rotate keys for specific functionality
4. **Backward Compatibility**: Default behavior preserved
5. **Clear Organization**: Easy to identify which key to use for new features

## Testing

After implementation, test both types of routes to ensure:
1. Authentication flows work correctly with AUTH_AES_KEY
2. Translation features work correctly with TRANSLATION_AES_KEY
3. Encryption/decryption is successful for both key types
4. Server can decrypt data encrypted with the appropriate keys

## Future Development

When adding new features:
- **Authentication-related**: Use `AESKeyType.auth`
- **Main app functionality**: Use `AESKeyType.translation`
- **Unsure**: Default is `AESKeyType.auth`, but consider the feature's purpose
