name: bla_application
description: "Translation app"
publish_to: "none"
version: 1.0.1+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter

  # view
  responsive_framework: ^1.5.1
  loading_animation_widget: ^1.3.0
  flutter_svg: ^2.0.17
  # mesh_gradient: ^1.3.8
  cupertino_icons: ^1.0.8
  flutter_dotenv: ^5.2.1
  country_code_picker: ^3.3.0
  pinput: ^5.0.1
  defer_pointer: ^0.0.2
  virtual_keyboard_custom_layout: ^1.0.1

  # dart
  equatable: ^2.0.7
  json_annotation: ^4.9.0
  freezed_annotation: ^2.4.4
  dartz: ^0.10.1

  # device
  flutter_hms_gms_availability: ^3.10.0
  path_provider: ^2.1.5
  permission_handler: ^11.4.0
  record: ^6.0.0

  # Data
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.3.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  cached_network_image: ^3.4.1
  flutter_secure_storage: ^9.2.4

  # Utilities
  easy_localization: ^3.0.7+1
  auto_route: ^9.3.0+1
  encrypt: ^5.0.3

  # Firebase
  firebase_core: ^3.11.0
  firebase_messaging: ^15.2.2
  firebase_analytics: ^11.4.2
  google_sign_in: ^6.2.1
  firebase_auth: ^5.5.1
  sign_in_with_apple: ^6.0.0
  # firebase_crashlytics: ^4.3.2 we will re-enable it when we need it in the future.

  # state management + DI
  get_it: ^8.0.3
  flutter_bloc: ^9.0.0
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  dropdown_flutter: ^1.0.1
  just_audio: ^0.9.46
  file_picker: ^9.2.0
  syncfusion_flutter_pdfviewer: ^28.2.12
  device_info_plus: ^11.3.0
  image_picker: ^1.1.2
  intl: ^0.19.0
  crypto: ^3.0.6
  flutter_inappwebview: ^6.1.5

dev_dependencies:
  flutter_launcher_icons: ^0.14.1
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # analysis
  flutter_lints: ^5.0.0

  # testing
  mockito: ^5.4.4

  # generators
  json_serializable: ^6.9.3
  auto_route_generator: ^9.3.1
  freezed: ^2.5.8
  build_runner: ^2.4.14

flutter_native_splash:
  color: "#ffffff"
  image: assets/logo/bla_app_icon.png
  android: true
  ios: true

flutter_icons:
  android: true
  ios: true
  image_path: "assets/logo/bla_app_icon.png"

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/logo/
    - assets/translations/
    - assets/images/illustrations/
    - assets/images/icons/
    - .env

  fonts:
    - family: Rabar013
      fonts:
        - asset: assets/fonts/Rabar_013.ttf
    - family: Rabar015
      fonts:
        - asset: assets/fonts/Rabar_015.ttf
    - family: Titillium
      fonts:
        - asset: assets/fonts/Titillium_Regular.ttf
