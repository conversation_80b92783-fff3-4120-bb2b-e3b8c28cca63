import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:responsive_framework/responsive_framework.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'src/app/widget/app.dart';
import 'src/core/utils/helpers/colored_logger.dart';
import 'src/core/utils/managers/database/database_manager.dart';
import 'src/core/utils/managers/http/bad_certificate_bypass.dart';
import 'src/injection.dart' as injection;
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'src/injection.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await dotenv.load(fileName: ".env");

  // Initialize device_info_plus and ensure it's registered
  await DeviceInfoPlugin().deviceInfo;

  // Initialize Firebase
  try {
    // Use default initialization for both platforms
    // This will use the GoogleService-Info.plist on iOS
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: Platform.isIOS ? 'AIzaSyAoABdX2pQjfVI3KT1Un5m5XWAZR4RiKzk' : 'AIzaSyBniDi4c6n5K_9GhoTHNeYR4SXvKEWO38Y',
        appId: Platform.isIOS ? '1:347435169642:ios:462fda67cf457d48e09713' : '1:347435169642:android:33a05d59140c930ce09713',
        messagingSenderId: '347435169642',
        projectId: 'bla-translate-cf63e',
        storageBucket: 'bla-translate-cf63e.firebasestorage.app',
      ),
    );
  } catch (e) {
    Log.e('Firebase initialization error: $e');
  }

  EasyLocalization.logger.enableBuildModes = [];
  HttpOverrides.global = BadCertificateBypass();

  await injection.init();
  await serviceLocator<DatabaseManager>().openBox();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  runApp(
    EasyLocalization(
      supportedLocales: [
        Locale('en', 'US'),
        // Locale('ku', 'CKB'),
        Locale('ar', 'AE'),
        Locale('ar', 'IQ'),
      ],
      path: "assets/translations",
      fallbackLocale: const Locale('en', 'US'),
      child: ResponsiveBreakpoints(
        breakpoints: [
          const Breakpoint(start: 0, end: 480, name: MOBILE),
          const Breakpoint(start: 481, end: 800, name: TABLET),
          const Breakpoint(start: 801, end: 1000, name: DESKTOP),
        ],
        child: const MyApp(),
      ),
    ),
  );
}
