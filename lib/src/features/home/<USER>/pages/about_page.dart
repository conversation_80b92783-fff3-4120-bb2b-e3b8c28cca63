import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../core/common/widgets/appbar/app_bar_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';

@RoutePage(name: "AboutPageRoute")
class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBarView(
        appBarTitle: 'about_us'.tr(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(context, 'what_is_bla'.tr()),
            const SizedBox(height: 16),

            TextView(
              text: 'bla_description'.tr(),
              style: textTheme.bodyLarge,
            ),
            const SizedBox(height: 32),

            //
            _buildSectionTitle(context, 'we_are_bla'.tr()),
            const SizedBox(height: 16),
            TextView(
              text: 'bla_name_origin'.tr(),
              style: textTheme.bodyLarge,
            ),

            const SizedBox(height: 16),
            TextView(
              text: 'bla_legacy'.tr(),
              style: textTheme.bodyLarge,
            ),

            //
            const SizedBox(height: 24),
            TextView(
              text: 'bla_features'.tr(),
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeaturesList(context),
            const SizedBox(height: 32),
            _buildSectionTitle(context, 'bla_api_enabled'.tr()),
            const SizedBox(height: 16),
            TextView(
              text: 'bla_api_description'.tr(),
              style: textTheme.bodyLarge,
            ),
            const SizedBox(height: 32),
            _buildSectionTitle(context, 'special_thanks'.tr()),
            const SizedBox(height: 16),
            _buildThanksList(context),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: colorScheme.primary.withValues(alpha: .3),
            width: 2,
          ),
        ),
      ),
      child: TextView(
        text: title,
        style: textTheme.headlineSmall?.copyWith(
          color: colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildFeaturesList(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    final features = [
      {
        'title': '${'text_translation'.tr()}: ',
        'description': 'text_translation_description'.tr(),
      },
      {
        'title': '${'document_translation'.tr()}: ',
        'description': 'document_translation_description'.tr(),
      },
      {
        'title': '${'ocr'.tr()}: ',
        'description': 'ocr_description'.tr(),
      },
      {
        'title': '${'dictionary'.tr()}: ',
        'description': 'dictionary_description'.tr(),
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: features.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(width: 16),
              TextView(text: "- "),
              const SizedBox(width: 6),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    children: [
                      TextSpan(
                        text: features[index]['title'],
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(
                        text: features[index]['description'],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThanksList(BuildContext context) {
    final companies = [
      'Microsoft Corporation',
      'Google LLC',
      'Yonescat Company',
      'AsoSoft Research Group',
      'Soran University',
      'iQ Group',
    ];

    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: companies.map((company) {
        return Chip(
          label: Text(company),
          backgroundColor: colorScheme.surface,
          labelStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimaryContainer,
          ),
        );
      }).toList(),
    );
  }
}
