import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../core/common/widgets/appbar/app_bar_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';

@RoutePage(name: "HowToUsePageRoute")
class HowToUsePage extends StatelessWidget {
  const HowToUsePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarView(
        appBarTitle: 'how_to_use_bla'.tr(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFeatureGuide(
              context,
              title: 'text_translation'.tr(),
              description: 'text_translation_description'.tr(),
              steps: [
                'select_source_language'.tr(),
                'input_text'.tr(),
                'choose_output_language'.tr(),
                'click_translate'.tr(),
                'receive_translation'.tr(),
              ],
              additionalInfo: [
                {
                  'title': 'tts_title'.tr(),
                  'content': 'tts_description'.tr(),
                  'icon': Icons.volume_up,
                },
                {
                  'title': 'stt_title'.tr(),
                  'content': 'stt_description'.tr(),
                  'icon': Icons.mic,
                },
                {
                  'title': 'spell_checker_title'.tr(),
                  'content': 'spell_checker_description'.tr(),
                  'icon': Icons.spellcheck,
                },
              ],
            ),
            const Divider(height: 32),
            _buildFeatureGuide(
              context,
              title: 'document_translation'.tr(),
              description: 'document_translation_description'.tr(),
              steps: [
                'choose_language'.tr(),
                'upload_document'.tr(),
                'translate_document'.tr(),
                'download_document'.tr(),
              ],
              additionalInfo: [
                {
                  'title': 'note'.tr(),
                  'content': 'document_alignment_note'.tr(),
                  'icon': Icons.info_outline,
                },
              ],
            ),
            const Divider(height: 32),
            _buildFeatureGuide(
              context,
              title: 'ocr'.tr(),
              description: 'ocr_description'.tr(),
              steps: [
                'upload_image'.tr(),
                'initiate_ocr'.tr(),
                'review_edit_text'.tr(),
                'save_copy_text'.tr(),
              ],
              additionalInfo: [],
            ),
            const Divider(height: 32),
            _buildFeatureGuide(
              context,
              title: 'dictionary'.tr(),
              description: 'dictionary_description'.tr(),
              steps: [
                'select_source_language'.tr(),
                'select_dictionary'.tr(),
                'enter_word'.tr(),
                'click_search'.tr(),
                'get_meaning'.tr(),
              ],
              additionalInfo: [],
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureGuide(
    BuildContext context, {
    required String title,
    required String description,
    required List<String> steps,
    required List<Map<String, dynamic>> additionalInfo,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // TODO: change those three fields
        TextView(
          text: title,
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextView(
          text: description,
          style: textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        TextView(
          text: "${'how_to_use'.tr()} $title?",
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: steps.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 16,
                  ),
                  TextView(
                    text: '${index + 1}.',
                    style: textTheme.bodyMedium?.copyWith(
                      // color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      steps[index],
                      style: textTheme.bodyLarge,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        if (additionalInfo.isNotEmpty) ...[
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: additionalInfo.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: RichText(
                  text: TextSpan(
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    children: [
                      TextSpan(
                        text: additionalInfo[index]['title'] as String,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(
                        text: additionalInfo[index]['content'] as String,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ],
    );
  }
}
