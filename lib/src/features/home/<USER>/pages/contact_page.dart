import 'package:auto_route/auto_route.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/common/widgets/app_space_widget.dart';
import '../../../../core/common/widgets/appbar/app_bar_view.dart';
import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/card_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_form_field_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../data/models/contact_request_model.dart';
import '../logic/contact/contact_cubit.dart';
import '../logic/contact/contact_state.dart';

@RoutePage(name: "ContactPageRoute")
class ContactPage extends StatefulWidget {
  const ContactPage({super.key});

  @override
  State<ContactPage> createState() => _ContactPageState();
}

class _ContactPageState extends State<ContactPage> {
  final _formKey = GlobalKey<FormState>();
  String _dialCode = '';
  final _nameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _emailController = TextEditingController();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    _subjectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarView(
        appBarTitle: 'contact_us'.tr(),
      ),
      body: BlocConsumer<ContactCubit, ContactState>(
        listener: (context, ContactState state) {
          state.maybeWhen(
            success: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('message_sent_successfully'.tr())),
              );
              context.router.back();
            },
            error: (error) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.error?.message ?? 'an_error_occurred'),
                ),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: CardView(
              color: Theme.of(context).cardColor,
              borderRadius: 12,
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextFormFieldView(
                      controller: _nameController,
                      textFormFieldTypes: TextFormFieldTypes.requiredText,
                      hintText: "enter_your_name".tr(),
                    ),
                    const SizedBox(height: 16),
                    TextFormFieldView(
                      controller: _emailController,
                      textFormFieldTypes: TextFormFieldTypes.email,
                      hintText: 'enter_your_email'.tr(),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(100)),
                          child: CountryCodePicker(
                            onChanged: (CountryCode countryCode) {
                              setState(() {
                                _dialCode = countryCode.dialCode ?? "";
                              });
                            },
                            initialSelection: '+964',
                            favorite: ['+964'],
                            showCountryOnly: false,
                            showOnlyCountryWhenClosed: false,
                            alignLeft: false, // Align text to the left
                            showFlag: false,
                            flagWidth: 25,
                            padding: const EdgeInsets.all(8),
                            backgroundColor: Colors.grey[200],
                          ),
                        ),
                        AppSpacer.p12(),
                        Expanded(
                          child: TextFormFieldView(
                            controller: _phoneNumberController,
                            textFormFieldTypes: TextFormFieldTypes.phone,
                            keyboardType: TextInputType.number,
                            hintText: "enter_your_phone_number".tr(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormFieldView(
                      controller: _subjectController,
                      textFormFieldTypes: TextFormFieldTypes.requiredText,
                      hintText: 'subject'.tr(),
                    ),
                    const SizedBox(height: 16),
                    TextFormFieldView(
                      controller: _descriptionController,
                      textFormFieldTypes: TextFormFieldTypes.requiredText,
                      hintText: 'enter_description_here'.tr(),
                      maxLines: 5,
                      maxLength: 1000,
                      buildCounter: (
                        p0, {
                        required currentLength,
                        required isFocused,
                        required maxLength,
                      }) {
                        return TextView(text: '$currentLength/$maxLength');
                      },
                    ),
                    const SizedBox(height: 24),
                    BlocConsumer<ContactCubit, ContactState>(
                      listener: (context, state) {},
                      builder: (context, state) {
                        return state.maybeWhen(
                          orElse: () => ButtonView(
                            title: 'submit'.tr(),
                            buttonType: ButtonType.solidButton,
                            onClick: () {
                              if (!_formKey.currentState!.validate()) {
                                return;
                              }
                              BlocProvider.of<ContactCubit>(context).sendContactRequest(
                                ContactRequestModel(
                                  name: _nameController.text,
                                  email: _emailController.text,
                                  subject: _subjectController.text,
                                  description: _descriptionController.text,
                                  dialCode: _dialCode,
                                  phone: _phoneNumberController.text,
                                ),
                              );
                            },
                            semanticLabelValue: 'Submit Contact Form',
                          ),
                          loading: () => const LoadingView(),
                          success: () => const SizedBox.shrink(),
                          error: (error) => const SizedBox.shrink(),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
