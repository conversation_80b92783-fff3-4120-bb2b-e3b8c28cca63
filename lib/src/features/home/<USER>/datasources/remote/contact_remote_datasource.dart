import 'package:dartz/dartz.dart';
import '../../../../../core/api/api.dart';
import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/utils/helpers/error_parser.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/http/http_methods.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/contact_request_model.dart';

abstract class ContactRemoteDataSource {
  Future<Either<ErrorModel, dynamic>> sendContactRequest(
    ContactRequestModel request,
  );
}

class ContactRemoteDataSourceImpl implements ContactRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  ContactRemoteDataSourceImpl({
    required this.httpManager,
    required this.aesEncryptionManager,
  });

  @override
  Future<Either<ErrorModel, dynamic>> sendContactRequest(
    ContactRequestModel request,
  ) async {
    try {
      final String encryptedPayload = await aesEncryptionManager.encryptedPayload(request.toJson());

      final response = await httpManager.request(
        path: Api().contact,
        method: HttpMethods.post,
        payload: {"data": encryptedPayload},
      );

      return Right(response.data);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
