import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../../../app/routes/router.gr.dart';
import '../../../../core/common/presentation/logic/user/user_cubit.dart';
import '../../../../core/common/widgets/app_space_widget.dart';
import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/enums/secure_storage_key.dart';
import '../../../../core/utils/managers/database/database_manager.dart';
import '../../../auth/presentation/logic/delete_account/delete_account_cubit.dart';
import '../../../auth/presentation/logic/delete_account/delete_account_state.dart';

import '../../../../core/common/presentation/logic/user/user_state.dart';

// Global key for the drawer
final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  static void openDrawer(BuildContext context) {
    scaffoldKey.currentState?.openDrawer();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final databaseManager = DatabaseManagerImpl();

    return Drawer(
      backgroundColor: colorScheme.surface,
      elevation: 0,
      child: Column(
        children: [
          BlocBuilder<UserCubit, UserState>(
            builder: (context, state) {
              return _buildHeader(
                context,
                name: state.user?.name ?? "",
                email: state.user?.email ?? "",
                colorScheme: colorScheme,
                textTheme: textTheme,
              );
            },
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: ListView(
                physics: NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(vertical: 16),
                children: [
                  _buildMenuItem(
                    context,
                    icon: Icons.info_outline_rounded,
                    title: 'about_us'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      context.router.push(const AboutPageRoute());
                    },
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.help_outline_rounded,
                    title: 'how_to_use_bla'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      context.router.push(const HowToUsePageRoute());
                    },
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.contact_support_outlined,
                    title: 'contact_us'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      context.router.push(const ContactPageRoute());
                    },
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.description_outlined,
                    title: 'term_and_conditions'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      context.router.push(const TermsAndConditionsPageRoute());
                    },
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.privacy_tip_outlined,
                    title: 'privacy_policy'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      context.router.push(const PrivacyPolicyPageRoute());
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Divider(
                      color: colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.delete_forever,
                    title: 'delete_account',
                    isDestructive: true,
                    onTap: () {
                      _showDeleteAccountConfirmationDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ),
          _buildMenuItem(
            context,
            icon: Icons.logout_rounded,
            title: 'logout',
            isDestructive: true,
            isSolid: true,
            onTap: () async {
              GoogleSignIn().signOut();
              await databaseManager.deleteSecureData(
                SecureStorageKey.token,
              );
              if (context.mounted) {
                context.read<UserCubit>().clearUser();
              }

              if (context.mounted) {
                context.router.pushAndPopUntil(
                  const LoginPageRoute(),
                  predicate: (route) => false,
                );
              }
            },
          ),
          AppSpacer.p16(),
        ],
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context, {
    required String name,
    required String email,
    required ColorScheme colorScheme,
    required TextTheme textTheme,
  }) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 24,
        bottom: 24,
        left: 24,
        right: 24,
      ),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 32,
                  backgroundColor: colorScheme.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.person_rounded,
                    size: 35,
                    color: colorScheme.primary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextView(
                      text: name,
                      style: textTheme.titleLarge?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    TextView(
                      text: email,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSolid = false,
    bool isDestructive = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    // Determine text color based on item type and style
    final Color textColor = _getTextColor(colorScheme, isDestructive, isSolid);

    // Determine background color based on style
    final Color backgroundColor = _getBackgroundColor(colorScheme, isDestructive, isSolid);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: ListTile(
          onTap: onTap,
          tileColor: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          leading: Icon(
            icon,
            color: textColor,
            size: 22,
          ),
          title: TextView(
            text: title,
            textAlignment: isSolid ? TextAlign.center : null,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
          ),
          minLeadingWidth: 24,
          horizontalTitleGap: 12,
        ),
      ),
    );
  }

  /// Helper method to determine text color based on item type and style
  Color _getTextColor(ColorScheme colorScheme, bool isDestructive, bool isSolid) {
    if (isDestructive) {
      // For destructive actions
      return isSolid
          ? colorScheme.onErrorContainer // White text on red background
          : colorScheme.error; // Red text on transparent background
    } else {
      // For normal actions
      return colorScheme.onSurface; // Normal text color
    }
  }

  /// Helper method to determine background color based on style
  Color _getBackgroundColor(ColorScheme colorScheme, bool isDestructive, bool isSolid) {
    if (isSolid) {
      // For solid buttons
      return isDestructive
          ? colorScheme.error // Red background for destructive
          : colorScheme.primaryContainer; // Primary color for normal
    } else {
      // For transparent buttons
      return Colors.transparent;
    }
  }

  void _showDeleteAccountConfirmationDialog(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.warning_amber_rounded,
          color: colorScheme.error,
          size: 36,
        ),
        title: TextView(
          text: 'delete_account_title',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.error,
          ),
          textAlignment: TextAlign.center,
        ),
        content: TextView(
          text: 'delete_account_sub_title',
          style: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
          textAlignment: TextAlign.center,
        ),
        actionsAlignment: MainAxisAlignment.center,
        actions: [
          OutlinedButton(
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: colorScheme.outline),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: TextView(
                text: 'general_cancel',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          BlocConsumer<DeleteAccountCubit, DeleteAccountState>(
            listener: (context, state) {
              state.maybeWhen(
                success: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: TextView(
                        text: 'delete_account_success_message',
                      ),
                      backgroundColor: colorScheme.primary,
                    ),
                  );

                  // Navigate to login page
                  context.router.pushAndPopUntil(
                    const LoginPageRoute(),
                    predicate: (route) => false,
                  );
                },
                error: (error) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: TextView(
                        text: error.error!.message ?? 'an_error_occurred',
                      ),
                      backgroundColor: colorScheme.error,
                    ),
                  );
                },
                orElse: () {},
              );
            },
            builder: (context, state) {
              final isLoading = state.maybeWhen(
                loading: () => true,
                orElse: () => false,
              );

              return ButtonView(
                semanticLabelValue: '',
                buttonType: ButtonType.outlinedButton,
                onClick: () {
                  context.read<DeleteAccountCubit>().deleteAccount();
                },
                title: "delete_account_title",
                isDisabled: isLoading,
                isExpanded: false,
                textStyle: textTheme.labelLarge?.copyWith(
                  color: colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
