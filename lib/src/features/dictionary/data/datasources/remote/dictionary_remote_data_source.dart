import 'dart:convert';

import '../../../../../core/api/api.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/http/http_methods.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/dictionary_model.dart';

abstract class IDictionaryRemoteDataSource {
  Future<DictionaryModel> searchWord({
    required String word,
    required String sourceLang,
    required String targetLang,
    required String dictionaryId,
  });
}

class DictionaryRemoteDataSource implements IDictionaryRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  DictionaryRemoteDataSource({required this.httpManager, required this.aesEncryptionManager});

  @override
  Future<DictionaryModel> searchWord({
    required String word,
    required String sourceLang,
    required String targetLang,
    required String dictionaryId,
  }) async {
    // Prepare the payload for the API request
    final payload = {
      'term_search': word.trim(),
      'language_select': sourceLang,
      'dictionary': dictionaryId,
    };

    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload,
      keyType: AESKeyType.translation,
    );

    // Make the API request
    final response = await httpManager.request(
      path: Api().textToDictionary,
      method: HttpMethods.post,
      payload: {"data": encryptedPayload},
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.translation,
    );

    decodedData = json.decode(decryptedData) as Map<String, dynamic>;

    final model = DictionaryModel.fromJson(decodedData);

    return model;
  }
}
