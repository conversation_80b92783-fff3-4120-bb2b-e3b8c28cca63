import 'package:dropdown_flutter/custom_dropdown.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/dictionary_constants.dart';

/// A model representing a dictionary item for the dropdown
class DictionaryItem {
  final String id;
  final String name;
  final IconData icon;

  const DictionaryItem(this.id, this.name, this.icon);

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DictionaryItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// A dropdown widget for selecting multiple dictionaries using the dropdown_flutter package
class DictionaryDropdown extends StatefulWidget {
  /// The currently selected dictionary IDs
  final List<String> selectedDictionaryIds;

  /// Callback function called when dictionary selection is changed
  final Function(List<String>) onDictionaryChanged;

  const DictionaryDropdown({
    super.key,
    required this.selectedDictionaryIds,
    required this.onDictionaryChanged,
  });

  @override
  State<DictionaryDropdown> createState() => _DictionaryDropdownState();
}

class _DictionaryDropdownState extends State<DictionaryDropdown> {
  List<DictionaryItem>? _selectedItems;
  bool _initialized = false;

  @override
  Widget build(BuildContext context) {
    // Get current locale for localized dictionary names
    final locale = context.locale.languageCode;

    // Create dictionary items from the constants list
    final dictionaryItems = _createDictionaryItems(locale);

    // Initialize or update selected items if needed
    if (!_initialized || widget.selectedDictionaryIds.isNotEmpty && (_selectedItems == null || _selectedItems!.isEmpty)) {
      _selectedItems = _getSelectedItems(dictionaryItems, widget.selectedDictionaryIds);
      _initialized = true;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownFlutter<DictionaryItem>.multiSelect(
        items: dictionaryItems,
        initialItems: _selectedItems ?? [],
        onListChanged: (selectedItems) {
          // removedLog log('Selected dictionaries: $selectedItems');
          setState(() {
            _selectedItems = selectedItems;
          });

          // Extract IDs from selected items and pass to callback
          final selectedIds = selectedItems.map((item) => item.id).toList();
          widget.onDictionaryChanged(selectedIds);
        },
      ),
    );
  }

  @override
  void didUpdateWidget(DictionaryDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDictionaryIds != widget.selectedDictionaryIds) {
      setState(() {
        // Will be updated in the next build
        _initialized = false;
      });
    }
  }

  /// Gets selected items from the existing items list by matching IDs
  List<DictionaryItem> _getSelectedItems(List<DictionaryItem> allItems, List<String> selectedIds) {
    if (selectedIds.isEmpty) return [];

    return allItems.where((item) => selectedIds.contains(item.id)).toList();
  }

  /// Creates a list of DictionaryItem objects from the dictionaries constant
  List<DictionaryItem> _createDictionaryItems(String locale) {
    // Filter out the placeholder entry and skip the first dictionary (id: '0')
    return dictionaries
        .where((dict) => dict.id != '0')
        .map((dict) => DictionaryItem(
              dict.id,
              dict.title.getLocalizedText(locale),
              _getDictionaryIcon(dict),
            ))
        .toList();
  }

  /// Returns an appropriate icon based on the dictionary's supported languages
  IconData _getDictionaryIcon(Dictionary dict) {
    if (dict.supportedLanguages.contains('en') && dict.supportedLanguages.contains('ck')) {
      return Icons.translate;
    } else if (dict.supportedLanguages.contains('ar')) {
      return Icons.language;
    } else if (dict.supportedLanguages.contains('fa')) {
      return Icons.menu_book;
    } else {
      return Icons.book;
    }
  }
}
