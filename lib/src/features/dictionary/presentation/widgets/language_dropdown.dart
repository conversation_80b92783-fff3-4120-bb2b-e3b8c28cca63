import 'package:dropdown_flutter/custom_dropdown.dart';
import 'package:flutter/material.dart';

/// A model representing a language item for the dropdown
class LanguageItem {
  final String code;
  final String name;
  final String localName;
  final IconData icon;

  const LanguageItem(this.code, this.name, this.localName, this.icon);

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageItem && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}

/// A dropdown widget for selecting a language
class LanguageDropdown extends StatefulWidget {
  /// The currently selected language code
  final String selectedLanguage;

  /// Callback function called when language is changed
  final Function(String) onLanguageChanged;

  const LanguageDropdown({
    super.key,
    required this.selectedLanguage,
    required this.onLanguageChanged,
  });

  @override
  State<LanguageDropdown> createState() => _LanguageDropdownState();
}

class _LanguageDropdownState extends State<LanguageDropdown> {
  late LanguageItem _selectedItem;

  @override
  void initState() {
    super.initState();
    // Initialize with default item
    _selectedItem = _createLanguageItems().first;
  }

  @override
  Widget build(BuildContext context) {
    // Create language items
    final languageItems = _createLanguageItems();

    // Update the selected item if needed
    if (_selectedItem.code != widget.selectedLanguage) {
      _selectedItem = _getSelectedItem(languageItems, widget.selectedLanguage);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownFlutter<LanguageItem>(
        items: languageItems,
        initialItem: _selectedItem,
        onChanged: (selectedItem) {
          if (selectedItem == null) return;

          // removedLog log('Selected language: $selectedItem');
          setState(() {
            _selectedItem = selectedItem;
          });

          // Pass language code to callback
          widget.onLanguageChanged(selectedItem.code);
        },
      ),
    );
  }

  @override
  void didUpdateWidget(LanguageDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedLanguage != widget.selectedLanguage) {
      setState(() {}); // Will trigger rebuild and update selected item
    }
  }

  /// Gets the selected language item from the items list
  LanguageItem _getSelectedItem(List<LanguageItem> allItems, String selectedCode) {
    try {
      return allItems.firstWhere((item) => item.code == selectedCode);
    } catch (e) {
      return allItems.first;
    }
  }

  /// Creates a list of LanguageItem objects with only the requested language codes
  List<LanguageItem> _createLanguageItems() {
    return [
      // English
      LanguageItem(
        'en',
        'English',
        'English',
        Icons.language,
      ),
      // Persian/Farsi
      LanguageItem(
        'fa',
        'Persian',
        'فارسی',
        Icons.language,
      ),
      // Northern Kurdish (Kurmanji)
      LanguageItem(
        'kmr',
        'Kurdish (Kurmanji)',
        'Kurdî (Kurmancî)',
        Icons.language,
      ),
      // Central Kurdish (Sorani)
      LanguageItem(
        'ckb',
        'Kurdish (Sorani)',
        'کوردی (سۆرانی)',
        Icons.language,
      ),
    ];
  }
}
