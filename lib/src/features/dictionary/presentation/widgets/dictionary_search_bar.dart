import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../../../../core/common/widgets/text_widgets/text_field_view.dart';

/// A search bar widget for the dictionary feature
class DictionarySearchBar extends StatefulWidget {
  /// The text editing controller for the search input
  final TextEditingController controller;

  /// Callback function when search is submitted
  final Function(String) onSubmitted;

  /// Callback function to clear the search
  final VoidCallback onClear;

  /// Whether the search is currently loading
  final bool isLoading;

  /// Whether the virtual keyboard is visible
  final bool isKeyboardVisible;

  /// Callback function when keyboard visibility changes
  final Function(bool) onKeyboardVisibilityChanged;

  const DictionarySearchBar({
    super.key,
    required this.controller,
    required this.onSubmitted,
    required this.onClear,
    required this.isKeyboardVisible,
    required this.onKeyboardVisibilityChanged,
    this.isLoading = false,
  });

  @override
  State<DictionarySearchBar> createState() => _DictionarySearchBarState();
}

class _DictionarySearchBarState extends State<DictionarySearchBar> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();

    // Add listener to detect keyboard actions and text changes
    widget.controller.addListener(_handleTextChange);
  }

  void _handleTextChange() {
    // Detect enter key press by looking for newline character
    if (widget.controller.text.endsWith('\n')) {
      final textWithoutNewline = widget.controller.text.replaceAll('\n', '');
      widget.controller.text = textWithoutNewline;

      if (textWithoutNewline.isNotEmpty) {
        widget.onSubmitted(textWithoutNewline);
      }

      _focusNode.unfocus();
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFieldView(
        textFieldController: widget.controller,
        textFieldTypes: TextFieldTypes.text,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: 'dictionary_page_search_here'.tr(),
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 16,
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  widget.isKeyboardVisible ? Icons.keyboard_hide : Icons.keyboard,
                  color: Colors.grey.shade600,
                ),
                onPressed: () {
                  FocusManager.instance.primaryFocus?.unfocus();

                  widget.onKeyboardVisibilityChanged(!widget.isKeyboardVisible);
                },
              ),
              if (widget.controller.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  color: Colors.grey.shade600,
                  onPressed: widget.onClear,
                ),
            ],
          ),
        ),
        maxLines: 1,
        autofocus: false,
        keyboardType: widget.isKeyboardVisible ? TextInputType.none : TextInputType.text,
      ),
    );
  }
}
