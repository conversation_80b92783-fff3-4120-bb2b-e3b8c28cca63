import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/cupertino.dart';

import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../domain/entities/dictionary_entity.dart';

class DictionaryResult extends StatelessWidget {
  final DictionaryEntity result;

  final Function(String)? onSuggestionClicked;

  const DictionaryResult({
    super.key,
    required this.result,
    this.onSuggestionClicked,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with word translation
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextView(
                    text: result.word,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy),
                  tooltip: 'ocr_page_copy_to_clipboard'.tr(),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: result.word));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: TextView(
                          text: 'ocr_page_text_copied_to_clipboard',
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // Translations section
          if (result.translations.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextView(
                    text: 'dictionary_page_Translations',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  ...result.translations.map(
                    (translation) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextView(
                            text: translation.translation,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  // fontFamily: "Rabar015",
                                  fontFamily: "Titillium",
                                ),
                          ),
                          if (translation.definitions.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            ...translation.definitions.map(
                              (definition) => Padding(
                                padding: const EdgeInsets.only(bottom: 4, left: 8),
                                child: TextView(
                                  text: '• $definition',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                            ),
                          ],
                          if (translation.examples.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            TextView(
                              text: 'dictionary_page_example_text',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontStyle: FontStyle.italic,
                                  ),
                            ),
                            ...translation.examples.map((example) => Padding(
                                  padding: const EdgeInsets.only(bottom: 4, left: 8),
                                  child: TextView(
                                    text: '- $example',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontStyle: FontStyle.italic,
                                        ),
                                  ),
                                )),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Suggestions section
          if (result.suggestions.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextView(
                    text: 'dictionary_page_suggestions_text',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 4,
                    runSpacing: 8,
                    children: result.suggestions
                        .map(
                          (suggestion) => Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              minSize: 0,
                              borderRadius: BorderRadius.circular(20),
                              pressedOpacity: 0.6,
                              onPressed: () {
                                if (onSuggestionClicked != null) {
                                  onSuggestionClicked!(suggestion);
                                }
                              },
                              child: Chip(
                                label: TextView(
                                  text: suggestion,
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
