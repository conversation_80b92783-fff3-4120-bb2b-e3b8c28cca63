import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:virtual_keyboard_custom_layout/virtual_keyboard_custom_layout.dart';

import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/constants/dictionary_constants.dart';
import '../logic/dictionary/dictionary_cubit.dart';
import '../widgets/dictionary_dropdown.dart';
import '../widgets/dictionary_result.dart';
import '../widgets/dictionary_search_bar.dart';
import '../widgets/language_dropdown.dart';
import '../../../../core/common/widgets/button_view.dart';

class DictionaryPage extends StatefulWidget {
  const DictionaryPage({super.key});

  @override
  State<DictionaryPage> createState() => _DictionaryPageState();
}

class _DictionaryPageState extends State<DictionaryPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedLanguage = LanguageCodes.centralKurdish;
  List<String> _selectedDictionaryIds = ['10'];

  bool _shiftEnabled = false;
  bool _isKeyboardVisible = false;

  final List<List<String>> _englishUSNormalKeys = [
    ["q", "w", "e", "r", "t", "y", "u", "i", "o", "p"],
    ["a", "s", "d", "f", "g", "h", "j", "k", "l", ";"],
    ["z", "x", "c", "v", "b", "n", "m", ",", "."],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  final List<List<String>> _englishUSShiftKeys = [
    ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"],
    ["A", "S", "D", "F", "G", "H", "J", "K", "L", ":"],
    ["Z", "X", "C", "V", "B", "N", "M", "<", ">"],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

// Kurdish Kurmanji Keyboard Layout
  final List<List<String>> _kurdishKurmanjiNormalKeys = [
    ["q", "w", "e", "r", "t", "y", "u", "î", "o", "p"],
    ["a", "s", "d", "f", "g", "h", "j", "k", "l", "ê"],
    ["z", "x", "c", "v", "b", "n", "m", "ç", "."],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  final List<List<String>> _kurdishKurmanjiShiftKeys = [
    ["Q", "W", "E", "R", "T", "Y", "U", "Î", "O", "P"],
    ["A", "S", "D", "F", "G", "H", "J", "K", "L", "Ê"],
    ["Z", "X", "C", "V", "B", "N", "M", "Ç", ":"],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

// Persian Keyboard Layout
  final List<List<String>> _persianNormalKeys = [
    ["ض", "ص", "ث", "ق", "ف", "غ", "ع", "ه", "خ", "ح"],
    ["ش", "س", "ی", "ب", "ل", "ا", "ت", "ن", "م", "ک"],
    ["ظ", "ط", "ز", "ر", "ذ", "د", "پ", "و", "."],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  final List<List<String>> _persianShiftKeys = [
    ["ۀ", "ً", "ٌ", "ّ", "َ", "ُ", "ِ", "آ", "]", "["],
    ["ؤ", "ئ", "ي", "إ", "أ", "ء", "ة", "«", "»", ":"],
    ["ك", "ْ", "‌", "ژ", "ٔ", "؛", "چ", ".", "؟"],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  final List<List<String>> _kurdishSoraniNormalKeys = [
    ["ق", "و", "ە", "ر", "ت", "ی", "و", "ی", "ۆ", "پ"],
    ["ا", "س", "د", "ف", "گ", "ه", "ژ", "ک", "ل", "ع"],
    ["ز", "خ", "ج", "ڤ", "ب", "ن", "م", "،", "."],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  final List<List<String>> _kurdishSoraniShiftKeys = [
    ["ق", "و", "ێ", "ڕ", "ث", "ی", "وو", "ی", "ۆ", "پ"],
    ["ئ", "ش", "ذ", "ف", "گ", "ح", "ژ", "ک", "ڵ", "غ"],
    ["ض", "غ", "چ", "ڤ", "ب", "ن", "م", "،", "."],
    ["SHIFT", "SPACE", "BACKSPACE"],
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<DictionaryCubit>().reset();
        // Ensure the language dropdown reflects the default language
        setState(() {
          _selectedLanguage = LanguageCodes.centralKurdish;
        });
      }
    });

    // Set initial keyboard state
    _isKeyboardVisible = false;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _searchWord(String word) {
    if (word.isEmpty) return;

    // Use the selected dictionary IDs directly
    final List<String> dictionaryList = List.from(_selectedDictionaryIds);

    // If no dictionaries are selected, default to Kurdistanica
    if (dictionaryList.isEmpty) {
      dictionaryList.add('10');

      // Update the state to show the default selection
      setState(() {
        _selectedDictionaryIds = ['10'];
      });
    }

    // Create a comma-separated string of dictionary IDs
    final String dictionaryIds = dictionaryList.join(',');

    context.read<DictionaryCubit>().searchWord(
          word: word,
          sourceLang: LanguageCodes.english, // We always search from English
          targetLang: _selectedLanguage,
          dictionaryId: dictionaryIds,
        );
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
    });
    context.read<DictionaryCubit>().reset();
  }

  void _toggleKeyboard(bool visible) {
    if (_isKeyboardVisible != visible) {
      setState(() {
        _isKeyboardVisible = visible;
      });
    }
  }

  void _handleKeyPress(VirtualKeyboardKey key) {
    if (key.action == VirtualKeyboardKeyAction.Shift) {
      setState(() {
        _shiftEnabled = !_shiftEnabled;
      });
    }
  }

  List<List<String>> _getKeyboardLayout() {
    switch (_selectedLanguage) {
      case LanguageCodes.english:
        return _shiftEnabled ? _englishUSShiftKeys : _englishUSNormalKeys;
      case LanguageCodes.northernKurdish:
        return _shiftEnabled ? _kurdishKurmanjiShiftKeys : _kurdishKurmanjiNormalKeys;
      case LanguageCodes.persian:
        return _shiftEnabled ? _persianShiftKeys : _persianNormalKeys;
      case LanguageCodes.centralKurdish:
        return _shiftEnabled ? _kurdishSoraniShiftKeys : _kurdishSoraniNormalKeys;
      default:
        return _shiftEnabled ? _englishUSShiftKeys : _englishUSNormalKeys;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: BlocConsumer<DictionaryCubit, DictionaryState>(
          listener: (context, state) {},
          builder: (context, state) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main card containing all inputs
                      Card(
                        color: Colors.white,
                        elevation: 1,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Language Dropdown
                              LanguageDropdown(
                                selectedLanguage: _selectedLanguage,
                                onLanguageChanged: (language) {
                                  setState(() {
                                    _selectedLanguage = language;
                                  });
                                },
                              ),
                              const SizedBox(height: 16),

                              // Dictionary Dropdown
                              DictionaryDropdown(
                                selectedDictionaryIds: _selectedDictionaryIds,
                                onDictionaryChanged: (dictionaryIds) {
                                  setState(() {
                                    _selectedDictionaryIds = dictionaryIds;
                                  });
                                },
                              ),
                              const SizedBox(height: 16),

                              // Search field
                              DictionarySearchBar(
                                controller: _searchController,
                                onSubmitted: _searchWord,
                                onClear: _clearSearch,
                                isKeyboardVisible: _isKeyboardVisible,
                                onKeyboardVisibilityChanged: _toggleKeyboard,
                              ),
                              const SizedBox(height: 16),

                              AnimatedSize(
                                duration: const Duration(milliseconds: 600),
                                curve: Curves.easeInOutCubicEmphasized,
                                alignment: Alignment.topCenter,
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 600),
                                  switchInCurve: Curves.easeInOutQuart,
                                  switchOutCurve: Curves.easeInOutCubicEmphasized,
                                  transitionBuilder: (Widget child, Animation<double> animation) {
                                    return FadeTransition(
                                      opacity: animation,
                                      child: ScaleTransition(
                                        scale: animation,
                                        alignment: Alignment.topCenter,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: _isKeyboardVisible
                                      ? Container(
                                          key: const ValueKey('keyboard'),
                                          decoration: ShapeDecoration(
                                            shape: ContinuousRectangleBorder(
                                              borderRadius: BorderRadius.circular(24),
                                              side: BorderSide(
                                                color: Theme.of(context).colorScheme.outline,
                                                width: 1,
                                              ),
                                            ),
                                          ),
                                          child: VirtualKeyboard(
                                            textColor: Theme.of(context).colorScheme.onSurface,
                                            fontSize: 20,
                                            defaultLayouts: [VirtualKeyboardDefaultLayouts.English],
                                            type: VirtualKeyboardType.Custom,
                                            keys: _getKeyboardLayout(),
                                            textController: _searchController,
                                            onKeyPress: _handleKeyPress,
                                            alwaysCaps: false,
                                            borderColor: Theme.of(context).colorScheme.outline,
                                          ),
                                        )
                                      : const SizedBox.shrink(key: ValueKey('empty')),
                                ),
                              ),

                              const SizedBox(height: 16),

                              ButtonView(
                                title: _selectedDictionaryIds.length > 1 ? "${'search_in_multiple_dictionaries_1'.tr()} ${_selectedDictionaryIds.length} ${'search_in_multiple_dictionaries_2'.tr()}" : 'search'.tr(),
                                buttonType: ButtonType.solidButton,
                                semanticLabelValue: 'search_dictionary'.tr(),
                                onClick: _selectedDictionaryIds.isEmpty ? () {} : () => _searchWord(_searchController.text),
                                isDisabled: _selectedDictionaryIds.isEmpty,
                                isExpanded: true,
                                haveBorder: false,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      state.maybeWhen(
                        success: (result) => DictionaryResult(
                          result: result,
                          onSuggestionClicked: (suggestion) {
                            _searchController.text = suggestion;
                            _searchWord(suggestion);
                          },
                        ),
                        orElse: () => const Center(child: LoadingView()),
                        initial: () => _buildInitialState(),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book,
            size: 64,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 16),
          TextView(
            text: 'dictionary_page_type_a_word_to_search_in_the_dictionary',
            textAlignment: TextAlign.center,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ],
      ),
    );
  }
}
