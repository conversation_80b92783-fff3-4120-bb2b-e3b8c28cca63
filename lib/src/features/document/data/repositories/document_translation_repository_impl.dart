import 'package:dartz/dartz.dart';

import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/utils/helpers/error_parser.dart';
import '../../domain/entities/document_translation_entity.dart';
import '../../domain/repositories/document_translation_repository.dart';
import '../datasources/remote/document_translation_remote_datasource.dart';
import '../models/document_translation_response_model.dart';

class DocumentTranslationRepositoryImpl implements DocumentTranslationRepository {
  final DocumentTranslationRemoteDataSource remoteDataSource;

  DocumentTranslationRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<ErrorModel, DocumentTranslationEntity>> translateDocument({
    required String targetLang,
    required String filePath,
  }) async {
    try {
      final DocumentTranslationResponseModel result = await remoteDataSource.translateDocument(targetLang: targetLang, filePath: filePath);

      return Right(result.toEntity());
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
