import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/enums/secure_storage_key.dart';
import '../../../../../core/utils/managers/database/database_manager.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../../../../injection.dart';
import '../../models/document_translation_response_model.dart';

abstract class DocumentTranslationRemoteDataSource {
  Future<DocumentTranslationResponseModel> translateDocument({
    required String targetLang,
    required String filePath,
  });
}

class DocumentTranslationRemoteDataSourceImpl implements DocumentTranslationRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  DocumentTranslationRemoteDataSourceImpl({required this.httpManager, required this.aesEncryptionManager});

  @override
  Future<DocumentTranslationResponseModel> translateDocument({
    required String targetLang,
    required String filePath,
  }) async {
    try {
      // Create a file object to make sure it exists
      final File file = File(filePath);
      if (!await file.exists()) {
        throw ErrorModel(error: Error(message: 'File not found: $filePath'));
      }

      // Get file name from path
      final String fileName = filePath.split('/').last;

      // Create Dio with appropriate timeouts
      final dio = Dio();
      dio.options.connectTimeout = const Duration(minutes: 2);
      dio.options.receiveTimeout = const Duration(minutes: 10);
      dio.options.sendTimeout = const Duration(minutes: 2);

      // Create multipart file
      final MultipartFile multipartFile = await MultipartFile.fromFile(
        filePath,
        filename: fileName,
      );

      var encryptedData = await aesEncryptionManager.encryptedPayload({'targetlang': targetLang});

      // Create FormData with the required fields
      final formData = FormData.fromMap({
        'file': multipartFile,
        'data': encryptedData,
      });

      /// gertting access token here

      final String? token = await serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token);

      // Add required headers
      final headers = {
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data',
        "Authorization": "Bearer $token",
      };

      // Make the API request
      final response = await dio.post(
        Api().docTranslate,
        data: formData,
        options: Options(
          headers: headers,
          validateStatus: (status) => true,
        ),
      );

      inspect(response);

      // Handle response
      if (response.statusCode == 200) {
        final String decryptedData = await aesEncryptionManager.decryptData(response.data["data"]);

        Map<String, dynamic> decodedData = json.decode(decryptedData) as Map<String, dynamic>;

        return DocumentTranslationResponseModel.fromJson(decodedData);
      } else {
        String errorMessage = 'Server error';
        if (response.data is Map) {
          errorMessage = response.data['message'] ?? 'Unknown server error';
        }
        throw ErrorModel(
          error: Error(message: 'Error ${response.statusCode}: $errorMessage'),
        );
      }
    } catch (e) {
      if (e is ErrorModel) {
        rethrow;
      }

      if (e is DioException && e.type == DioExceptionType.receiveTimeout) {
        throw ErrorModel(
          error: Error(
            message: 'The document translation is taking longer than expected. Please try a smaller document or try again later.',
          ),
        );
      }

      throw ErrorModel(error: Error(message: e.toString()));
    }
  }
}
