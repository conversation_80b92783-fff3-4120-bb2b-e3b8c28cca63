import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/document_translation_entity.dart';
import '../../../domain/repositories/document_translation_repository.dart';

part 'document_translation_cubit.freezed.dart';
part 'document_translation_state.dart';

class DocumentTranslationCubit extends Cubit<DocumentTranslationState> {
  final DocumentTranslationRepository _repository;

  DocumentTranslationCubit(this._repository) : super(const DocumentTranslationState.initial());

  void reset() {
    emit(const DocumentTranslationState.initial());
  }

  Future<void> translateDocument({
    required String targetLang,
    required String filePath,
  }) async {
    emit(const DocumentTranslationState.loading());

    try {
      final result = await _repository.translateDocument(
        targetLang: targetLang,
        filePath: filePath,
      );

      result.fold(
        (failure) {
          emit(DocumentTranslationState.error(failure.toString()));
        },
        (data) {
          emit(DocumentTranslationState.success(data));
        },
      );
    } catch (e) {
      final errorMessage = 'Unexpected error during document translation: ${e.toString()}';
      emit(DocumentTranslationState.error(errorMessage));
    }
  }
}
