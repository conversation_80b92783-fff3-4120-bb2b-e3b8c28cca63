import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';

/// A page that displays a PDF document in a full-screen viewer.
class PdfViewerPage extends StatefulWidget {
  final String pdfUrl;
  final String? title;

  const PdfViewerPage({
    super.key,
    required this.pdfUrl,
    this.title,
  });

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  final PdfViewerController _pdfViewerController = PdfViewerController();
  bool _isLoading = true;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextView(text: widget.title ?? 'pdf_viewer_page_title'),
        centerTitle: true,
      ),
      body: _buildPdfViewer(),
      floatingActionButton: _isLoading || _errorMessage != null
          ? null
          : FloatingActionButton(
              onPressed: _downloadPdf,
              child: const Icon(Icons.download),
            ),
    );
  }

  Widget _buildPdfViewer() {
    return Stack(
      children: [
        SfPdfViewer.network(
          widget.pdfUrl,
          controller: _pdfViewerController,
          canShowScrollHead: true,
          canShowScrollStatus: true,
          enableDoubleTapZooming: true,
          enableTextSelection: true,
          onDocumentLoaded: (PdfDocumentLoadedDetails details) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _errorMessage = '${"pdf_viewer_page_faild_load_pdf".tr()} ${details.error}';
              });
            }
          },
        ),
        if (_isLoading)
          Container(
            color: Colors.white,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const LoadingView(),
                  const SizedBox(height: 16),
                  TextView(text: 'pdf_viewer_page_loading_pdf'),
                ],
              ),
            ),
          ),
        if (_errorMessage != null) _buildErrorView(),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[700], size: 48),
            const SizedBox(height: 16),
            TextView(
              text: 'general_error',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.red[700],
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            TextView(
              text: _errorMessage!,
              textAlignment: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: TextView(text: 'general_go_back'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadPdf() async {
    final uri = Uri.parse(widget.pdfUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: TextView(text: 'pdf_viewer_page_could_not_open_file'),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }
}
