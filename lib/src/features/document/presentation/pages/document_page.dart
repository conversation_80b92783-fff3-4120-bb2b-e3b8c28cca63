import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dropdown_flutter/custom_dropdown.dart';

import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../domain/entities/document_translation_entity.dart';
import '../logic/document_translation/document_translation_cubit.dart';
import '../widgets/document_success_view.dart';
import '../widgets/document_selector_view.dart';
import '../widgets/loading_document_translation.dart';
import '../widgets/selected_file_view.dart';
import 'pdf_viewer_page.dart';

class DocumentPage extends StatefulWidget {
  const DocumentPage({super.key});

  @override
  State<DocumentPage> createState() => _DocumentPageState();
}

class _DocumentPageState extends State<DocumentPage> {
  String? _selectedFilePath;
  String? _selectedFileName;
  String? _translatedFileUrl;
  String _selectedLanguage = 'ku';

  // Language model with display names and codes
  final List<Map<String, String>> _languageModels = [
    {'name': 'Kurdish (Sorani)', 'code': 'ku'},
    {'name': 'Arabic', 'code': 'ar'},
    {'name': 'English', 'code': 'en'},
    {'name': 'Turkish', 'code': 'tr'},
    {'name': 'Persian', 'code': 'fa'},
  ];

  // Helper getter to get just the language display names for dropdown
  List<String> get _languageNames => _languageModels.map((lang) => lang['name']!).toList();

  // Get language code from display name
  String _getLanguageCode(String languageName) {
    final language = _languageModels.firstWhere(
      (lang) => lang['name'] == languageName,
      orElse: () => {'name': 'Kurdish (Sorani)', 'code': 'ku'},
    );
    return language['code']!;
  }

  // Get display name from language code
  String _getLanguageDisplayName() {
    final language = _languageModels.firstWhere(
      (lang) => lang['code'] == _selectedLanguage,
      orElse: () => {'name': 'Kurdish (Sorani)', 'code': 'ku'},
    );
    return language['name']!;
  }

  // Helper method to check if device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 is API level 33
    }
    return false;
  }

  Future<void> _checkAndRequestPermissions() async {
    if (Platform.isAndroid) {
      if (await _isAndroid13OrHigher()) {
        // For Android 13+, request READ_MEDIA_IMAGES permission
        if (await Permission.photos.request().isGranted) {
          await _pickFile();
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: TextView(text: 'ocr_page_photos_permission_required')),
          );
        }
      } else {
        // For Android 12 and below, use storage permission
        if (await Permission.storage.request().isGranted) {
          await _pickFile();
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: TextView(text: 'ocr_page_storage_permission_required')),
          );
        }
      }
    } else {
      // For iOS and other platforms
      await _pickFile();
    }
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['doc', 'docx', 'pdf', 'csv', 'txt', 'xlx', 'xls'],
      );

      if (result != null) {
        setState(() {
          _selectedFilePath = result.files.single.path;
          _selectedFileName = result.files.single.name;
        });
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: TextView(
            text: '${"ocr_page_error_picking_file".tr()} ${e.toString()}',
          ),
        ),
      );
    }
  }

  Future<void> _translateDocument() async {
    if (_selectedFilePath == null) return;

    context.read<DocumentTranslationCubit>().translateDocument(
          targetLang: _selectedLanguage,
          filePath: _selectedFilePath!,
        );
  }

  Future<void> _downloadFile() async {
    if (_translatedFileUrl == null) return;

    final uri = Uri.parse(_translatedFileUrl!);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: TextView(text: 'pdf_viewer_page_could_not_open_file')),
      );
    }
  }

  // Check if the file is a PDF
  bool _isPdf() {
    if (_translatedFileUrl == null) return false;
    return _translatedFileUrl!.toLowerCase().endsWith('.pdf');
  }

  // Navigate to PDF viewer page
  void _viewPdf() {
    if (_translatedFileUrl == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfViewerPage(
          pdfUrl: _translatedFileUrl!,
          title: 'document_page_translated_document',
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    context.read<DocumentTranslationCubit>().reset();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DocumentTranslationCubit, DocumentTranslationState>(
      listener: (context, state) {
        state.maybeWhen(
          success: (data) {
            setState(() {
              _translatedFileUrl = data.filename;
            });
          },
          error: (message) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: TextView(text: message)),
            );
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        return Column(
          children: [
            Expanded(
              child: state.maybeWhen(
                loading: () => const LoadingDocumentTranslation(),
                success: (DocumentTranslationEntity translatedFile) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SingleChildScrollView(
                      child: DocumentSuccessView(
                        translatedFileUrl: translatedFile.filename,
                        isPdf: _isPdf(),
                        onViewPdf: _viewPdf,
                        onDownload: _downloadFile,
                        onTranslateAnother: () {
                          setState(() {
                            _translatedFileUrl = null;
                            _selectedFilePath = null;
                            _selectedFileName = null;
                          });
                          // Reset to initial state
                          context.read<DocumentTranslationCubit>().reset();
                        },
                      ),
                    ),
                  );
                },
                error: (_) => _buildInitialContent(),
                initial: () => _buildInitialContent(),
                orElse: () => _buildInitialContent(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInitialContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),
            Column(
              children: [
                TextView(
                  text: 'document_page_title',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                TextView(
                  text: 'document_page_description',
                  textAlignment: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ],
            ),
            const SizedBox(height: 56),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextView(
                  text: 'document_page_target_language',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                DropdownFlutter<String>(
                  hintText: 'select_language'.tr(),
                  items: _languageNames,
                  initialItem: _getLanguageDisplayName(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedLanguage = _getLanguageCode(value);
                      });
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),
            if (_selectedFilePath == null) ...[
              DocumentSelectorView(
                onSelectFile: _checkAndRequestPermissions,
              ),
            ] else ...[
              SelectedFileView(
                fileName: _selectedFileName ?? '',
                onClear: () {
                  setState(() {
                    _selectedFilePath = null;
                    _selectedFileName = null;
                  });
                },
              ),
            ],
            const SizedBox(height: 20),
            ButtonView(
              buttonType: ButtonType.solidButton,
              semanticLabelValue: 'Translate Document',
              onClick: _selectedFilePath != null ? _translateDocument : () {},
              title: 'document_page_translate_document_button',
              isDisabled: _selectedFilePath == null,
              isExpanded: true,
            ),
          ],
        ),
      ),
    );
  }
}
