import 'package:flutter/material.dart';

import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/card_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';

class DocumentSuccessView extends StatelessWidget {
  final String? translatedFileUrl;
  final bool isPdf;
  final VoidCallback onViewPdf;
  final VoidCallback onDownload;
  final VoidCallback onTranslateAnother;

  const DocumentSuccessView({
    super.key,
    required this.translatedFileUrl,
    required this.isPdf,
    required this.onViewPdf,
    required this.onDownload,
    required this.onTranslateAnother,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CardView(
          borderRadius: 16,
          elevation: 1,
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green[600],
                size: 64,
              ),
              const SizedBox(height: 20),
              TextView(
                text: 'document_translation_success_translattion_complete',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green[800],
                    ),
                textAlignment: TextAlign.center,
              ),
              const SizedBox(height: 12),
              TextView(
                text: 'document_translation_success_your_document_has_been_successfully_translated',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.green[700],
                    ),
                textAlignment: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ButtonView(
                buttonType: ButtonType.solidButton,
                semanticLabelValue: 'Open Document URL',
                onClick: onDownload,
                title: 'document_translation_success_open_document_url',
                isExpanded: true,
              ),
              const SizedBox(height: 12),
              ButtonView(
                buttonType: ButtonType.textButton,
                semanticLabelValue: 'Translate Another Document',
                onClick: onTranslateAnother,
                title: 'document_translation_success_translate_another_document',
                buttonColor: Colors.white,
                haveBorder: true,
                isExpanded: true,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        if (isPdf) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AspectRatio(
              aspectRatio: 1 / 1.414, // A4 paper ratio (width:height)
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: .3)),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SfPdfViewer.network(
                  translatedFileUrl!,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
