import '../../../../../core/enums/auth_field_type.dart';

class ResetPasswordPayload {
  final String? email;
  final String? phone;
  final String password;
  final String confirmPassword;
  final AuthFieldType type;
  final String? token;
  final String? recaptchaToken;

  ResetPasswordPayload({
    this.email,
    this.phone,
    required this.password,
    required this.confirmPassword,
    required this.type,
    this.token,
    this.recaptchaToken,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'password': password,
      'confirm_password': confirmPassword,
      'type': type.name,
    };

    // Only include email or phone based on the type
    if (type == AuthFieldType.email) {
      data['email'] = email;
    } else if (type == AuthFieldType.phone) {
      data['phone'] = phone;
    }

    // Include token if available
    if (token != null) {
      data['token'] = token;
    }

    // Include recaptcha token if available
    if (recaptchaToken != null) {
      data['recaptcha'] = recaptchaToken;
    }

    return data;
  }

  factory ResetPasswordPayload.fromJson(Map<String, dynamic> json) {
    final typeStr = json['type'] as String;
    final authFieldType = AuthFieldType.values.firstWhere(
      (e) => e.name == typeStr,
      orElse: () => AuthFieldType.email,
    );

    return ResetPasswordPayload(
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      password: json['password'] as String,
      confirmPassword: json['confirm_password'] as String,
      type: authFieldType,
      token: json['token'] as String?,
      recaptchaToken: json['recaptcha'] as String?,
    );
  }
}
