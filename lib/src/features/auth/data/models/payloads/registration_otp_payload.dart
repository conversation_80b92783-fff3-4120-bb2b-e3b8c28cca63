import '../../../../../core/enums/auth_field_type.dart';

class RegistrationOtpPayload {
  final String? email;
  final String? phone;
  final AuthFieldType type;
  final String? recaptchaToken;

  RegistrationOtpPayload({
    this.email,
    this.phone,
    required this.type,
    this.recaptchaToken,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'type': type.name,
    };

    // Only include email or phone based on the type
    if (type == AuthFieldType.email) {
      data['email'] = email;
    } else if (type == AuthFieldType.phone) {
      data['phone'] = phone;
    }

    // Include recaptcha token if available
    if (recaptchaToken != null) {
      data['recaptcha'] = recaptchaToken;
    }

    return data;
  }

  factory RegistrationOtpPayload.fromJson(Map<String, dynamic> json) {
    final typeStr = json['type'] as String;
    final authFieldType = AuthFieldType.values.firstWhere(
      (e) => e.name == typeStr,
      orElse: () => AuthFieldType.email,
    );

    return RegistrationOtpPayload(
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      type: authFieldType,
      recaptchaToken: json['recaptcha'] as String?,
    );
  }
}
