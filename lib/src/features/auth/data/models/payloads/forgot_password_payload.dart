import '../../../../../core/enums/auth_field_type.dart';

class ForgotPasswordPayload {
  final String? email;
  final String? phone;
  final AuthFieldType type;
  final String? token;
  final String? recaptchaToken;

  ForgotPasswordPayload({
    this.phone,
    required this.type,
    this.email,
    this.token,
    this.recaptchaToken,
  });

  // create a value to get which credential value is filled
  String get credentialValue {
    if (type == AuthFieldType.email) {
      return email ?? '';
    } else {
      return phone ?? '';
    }
  }

  // how to use this in test file to test it and its outcome.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    // Only include email or phone based on the type
    if (type == AuthFieldType.email) {
      data['email'] = email;
    } else if (type == AuthFieldType.phone) {
      data['phone'] = phone;
    }

    data['type'] = type.name;

    // Include recaptcha token if available
    if (recaptchaToken != null) {
      data['recaptcha'] = recaptchaToken;
    }

    return data;
  }

  factory ForgotPasswordPayload.fromJson(Map<String, dynamic> json) {
    final typeStr = json['type'] as String;
    final authFieldType = AuthFieldType.values.firstWhere(
      (e) => e.name == typeStr,
      orElse: () => AuthFieldType.email,
    );

    return ForgotPasswordPayload(
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      type: authFieldType,
      token: json['token'] as String?,
      recaptchaToken: json['recaptcha'] as String?,
    );
  }
}
