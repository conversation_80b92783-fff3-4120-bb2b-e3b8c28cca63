import '../../../../core/common/domain/entities/UserEntity/role.dart';
import '../../../../core/common/domain/entities/UserEntity/user_entity.dart';
import '../models/login/user_model.dart';

class UserEntityMapper {
  final UserModel? userModel;

  UserEntityMapper({this.userModel});

  UserEntity toUser() {
    return UserEntity(
      id: userModel?.id ?? 0,
      name: userModel?.name ?? "",
      email: userModel?.email ?? "",
      emailVerifiedAt: userModel?.emailVerifiedAt ?? "",
      otp: userModel?.otp ?? 0,
      otpExpiredAt: userModel?.otpExpiredAt ?? "",
      phone: userModel?.phone ?? "",
      companyName: userModel?.companyName ?? "",
      image: userModel?.image ?? "",
      roleId: userModel?.roleId ?? 0,
      planId: userModel?.planId ?? 0,
      verifyString: userModel?.verifyString ?? "",
      isActive: userModel?.isActive ?? false,
      isDeleted: userModel?.isDeleted ?? false,
      ip: userModel?.ip ?? "",
      createdAt: userModel?.createdAt ?? "",
      updatedAt: userModel?.updatedAt ?? "",
      role: userModel?.role != null
          ? Role.fromJson(userModel!.role!.toJson())
          : Role(
              id: 0,
              name: "",
              description: "",
              guardName: "",
              isActive: 0,
              createdAt: "",
              updatedAt: "",
            ),
    );
  }
}
