import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/utils/managers/database/database_manager.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/http/http_methods.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/payloads/check_exist_payload.dart';
import '../../models/payloads/forgot_password_payload.dart';
import '../../models/payloads/login_payload.dart';
import '../../models/payloads/registration_otp_payload.dart';
import '../../models/payloads/reset_password_payload.dart';
import '../../models/payloads/signup_payload.dart';
import '../../models/payloads/social_login_payload.dart';
import '../../models/payloads/verify_otp_payload.dart';

abstract class AuthRemoteDataSource {
  // Login
  Future<Map<String, dynamic>> login(
    LoginPayload loginPayload,
  );

  // Social Login
  Future<Map<String, dynamic>> socialLogin(
    SocialLoginPayload socialLoginPayload,
  );

  // signup
  Future<Map<String, dynamic>> signup(
    SignupPayload signupPayload,
  );

  // Request Forgot Password
  Future<Map<String, dynamic>> requestForgotPassword(
    ForgotPasswordPayload payload,
  );

  // Verify OTP
  Future<Map<String, dynamic>> verifyOtp(
    VerifyOTPPayload payload,
  );

  // Reset Password
  Future<Map<String, dynamic>> resetPassword(
    ResetPasswordPayload payload,
  );

  // Delete Account
  Future<Map<String, dynamic>> deleteAccount();

  // Check if email/phone already exists
  Future<void> checkAlreadyExist(
    CheckExistPayload payload,
  );

  // Request Registration OTP
  Future<Map<String, dynamic>> requestRegistrationOtp(
    RegistrationOtpPayload payload,
  );
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  AuthRemoteDataSourceImpl(this.httpManager, this.aesEncryptionManager);

  DatabaseManager databaseManager = DatabaseManagerImpl();

  @override
  Future<Map<String, dynamic>> login(LoginPayload loginPayload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      loginPayload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().login,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData['data'] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> socialLogin(SocialLoginPayload socialLoginPayload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      socialLoginPayload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().login,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData['data'] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> signup(SignupPayload signupPayload) async {
    // Encrypt the payload
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      signupPayload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().signup,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );
    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData["data"] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> requestForgotPassword(ForgotPasswordPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().forgotPassword,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData["data"] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> verifyOtp(VerifyOTPPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().verifyOtp,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> resetPassword(ResetPasswordPayload payload) async {
    // Encrypt the payload
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().resetPassword,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData["message"] = json.decode(decryptedData);

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> requestRegistrationOtp(RegistrationOtpPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.auth,
    );

    final Response response = await httpManager.request(
      path: Api().registrationOtp,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.auth,
    );

    decodedData['data'] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> deleteAccount() async {
    final Response response = await httpManager.request(
      path: Api().deleteAccount,
      method: HttpMethods.post,
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }

  @override
  Future<void> checkAlreadyExist(CheckExistPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.auth,
    );

    await httpManager.request(
      path: Api().checkAlreadyExist,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
    );
  }
}
