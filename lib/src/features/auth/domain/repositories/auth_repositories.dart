import 'package:dartz/dartz.dart';

import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/common/domain/entities/UserEntity/user_entity.dart';
import '../../data/models/payloads/check_exist_payload.dart';
import '../../data/models/payloads/forgot_password_payload.dart';
import '../../data/models/payloads/login_payload.dart';
import '../../data/models/payloads/registration_otp_payload.dart';
import '../../data/models/payloads/reset_password_payload.dart';
import '../../data/models/payloads/signup_payload.dart';
import '../../data/models/payloads/social_login_payload.dart';
import '../../data/models/payloads/verify_otp_payload.dart';

abstract class AuthRepository {
  // login
  Future<Either<ErrorModel, UserEntity>> login(
    LoginPayload loginPayload,
  );

  // social Login
  Future<Either<ErrorModel, UserEntity>> socialLogin(
    SocialLoginPayload socialLoginPayload,
  );

  // signup
  Future<Either<ErrorModel, UserEntity>> signup(
    SignupPayload signupPayload,
  );

  // Request Forgot Password
  Future<Either<ErrorModel, String>> requestForgotPassword(
    ForgotPasswordPayload payload,
  );

  // Verify OTP
  Future<Either<ErrorModel, String>> verifyOtp(
    VerifyOTPPayload payload,
  );

  // Reset Password
  Future<Either<ErrorModel, bool>> resetPassword(
    ResetPasswordPayload payload,
  );

  // Delete Account
  Future<Either<ErrorModel, bool>> deleteAccount();

  // Check if email/phone already exists
  Future<Either<ErrorModel, bool>> checkAlreadyExist(
    CheckExistPayload payload,
  );

  // Request Registration OTP
  Future<Either<ErrorModel, String>> requestRegistrationOtp(
    RegistrationOtpPayload payload,
  );
}
