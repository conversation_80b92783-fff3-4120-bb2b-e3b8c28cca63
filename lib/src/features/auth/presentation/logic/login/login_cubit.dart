import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/enums/login_type.dart';
import '../../../data/models/payloads/login_payload.dart';
import '../../../data/models/payloads/social_login_payload.dart';
import '../../../domain/repositories/auth_repositories.dart';

import 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  final AuthRepository repository;

  LoginCubit({required this.repository}) : super(const LoginState());

  Future<void> login({
    required String email,
    required String password,
    required LoginType loginType,
  }) async {
    emit(state.copyWith(emailLogin: const EmailLoginState.initial()));
    emit(state.copyWith(emailLogin: const EmailLoginState.loading()));

    final loginPayload = LoginPayload(
      email: loginType == LoginType.email ? email : null,
      phone: loginType == LoginType.phone ? email : null,
      password: password,
      loginType: loginType,
    );

    final result = await repository.login(loginPayload);

    result.fold(
      (ErrorModel error) {
        emit(state.copyWith(emailLogin: EmailLoginState.error(error)));
        emit(state.copyWith(emailLogin: const EmailLoginState.initial()));
      },
      (user) {
        emit(state.copyWith(emailLogin: EmailLoginState.success(user)));
        emit(state.copyWith(emailLogin: const EmailLoginState.initial()));
      },
    );
  }

  Future<void> loginWithGoogle({required String token}) async {
    emit(state.copyWith(googleLogin: const GoogleLoginState.initial()));
    emit(state.copyWith(googleLogin: const GoogleLoginState.loading()));

    final socialLoginPayload = SocialLoginPayload(
      token: token,

      loginType: LoginType.google,

    );

    final result = await repository.socialLogin(socialLoginPayload);

    result.fold(
      (ErrorModel error) {
        emit(state.copyWith(googleLogin: GoogleLoginState.error(error)));
        emit(state.copyWith(googleLogin: const GoogleLoginState.initial()));
      },
      (user) {
        emit(state.copyWith(googleLogin: GoogleLoginState.success(user)));
        emit(state.copyWith(googleLogin: const GoogleLoginState.initial()));
      },
    );
  }

  Future<void> loginWithApple({required String token}) async {
    emit(state.copyWith(appleLogin: const AppleLoginState.initial()));
    emit(state.copyWith(appleLogin: const AppleLoginState.loading()));

    final socialLoginPayload = SocialLoginPayload(
      token: token,

      loginType: LoginType.apple,

    );

    final result = await repository.socialLogin(socialLoginPayload);

    result.fold(
      (ErrorModel error) {
        emit(state.copyWith(appleLogin: AppleLoginState.error(error)));
        emit(state.copyWith(appleLogin: const AppleLoginState.initial()));
      },
      (user) {
        emit(state.copyWith(appleLogin: AppleLoginState.success(user)));
        emit(state.copyWith(appleLogin: const AppleLoginState.initial()));
      },
    );
  }
}
