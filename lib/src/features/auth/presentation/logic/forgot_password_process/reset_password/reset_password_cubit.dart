import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../../core/enums/auth_field_type.dart';
import '../../../../data/models/payloads/reset_password_payload.dart';
import '../../../../domain/repositories/auth_repositories.dart';
import 'reset_password_state.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  final AuthRepository repository;

  ResetPasswordCubit({required this.repository}) : super(const ResetPasswordState.initial());

  Future<void> resetPassword({
    required String identifier,
    required AuthFieldType identifierType,
    required String password,
    String? token,
    String? recaptchaToken,
  }) async {
    emit(const ResetPasswordState.loading());

    final result = await repository.resetPassword(
      ResetPasswordPayload(
        email: identifierType == AuthFieldType.email ? identifier : null,
        phone: identifierType == AuthFieldType.phone ? identifier : null,
        password: password,
        confirmPassword: password,
        type: identifierType,
        token: token,
        recaptchaToken: recaptchaToken,
      ),
    );

    result.fold(
      (ErrorModel error) {
        emit(ResetPasswordState.error(error));
      },
      (success) {
        emit(const ResetPasswordState.success());
      },
    );
  }
}
