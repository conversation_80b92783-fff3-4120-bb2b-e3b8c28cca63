import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../../core/enums/auth_field_type.dart';
import '../../../../data/models/payloads/registration_otp_payload.dart';
import '../../../../domain/repositories/auth_repositories.dart';
import 'registration_otp_state.dart';

class RegistrationOtpCubit extends Cubit<RegistrationOtpState> {
  final AuthRepository repository;

  RegistrationOtpCubit({
    required this.repository,
  }) : super(const RegistrationOtpState.initial());

  Future<void> requestRegistrationOtp({
    String? email,
    String? phone,
    required AuthFieldType type,
    String? recaptchaToken,
  }) async {
    emit(const RegistrationOtpState.loading());

    // Log the request details for debugging

    final payload = RegistrationOtpPayload(
      email: type == AuthFieldType.email ? email : null,
      phone: type == AuthFieldType.phone ? phone : null,
      type: type,
      recaptchaToken: recaptchaToken,
    );

    final result = await repository.requestRegistrationOtp(payload);

    result.fold(
      (ErrorModel error) {
        emit(RegistrationOtpState.error(error));
      },
      (token) {
        emit(RegistrationOtpState.success(token));
      },
    );
  }
}
