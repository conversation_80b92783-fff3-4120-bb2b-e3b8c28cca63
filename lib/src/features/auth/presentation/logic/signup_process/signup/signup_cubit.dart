import 'package:bla_application/src/core/common/data/models/error_model/error_model.dart';
import 'package:bla_application/src/core/common/domain/entities/UserEntity/user_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../core/enums/auth_field_type.dart';
import '../../../../data/models/payloads/signup_payload.dart';
import '../../../../domain/repositories/auth_repositories.dart';
import 'signup_state.dart';

class SignupCubit extends Cubit<SignupState> {
  final AuthRepository repository;

  SignupCubit({required this.repository}) : super(const SignupState());

  Future<void> signup({
    required String firstName,
    required String lastName,
    String? email,
    String? phone,
    required String password,
    String? token,
    AuthFieldType registerType = AuthFieldType.email,
    String dialCode = '',
  }) async {
    emit(state.copyWith(emailSignup: const EmailSignupState.initial()));
    emit(state.copyWith(emailSignup: const EmailSignupState.loading()));

    final signupPayload = SignupPayload(
      name: '$firstName $lastName',
      email: email,
      phone: phone,
      dialCode: dialCode,
      password: password,
      confirmPassword: password,
      registerType: registerType,
      token: token,
    );

    final result = await repository.signup(signupPayload);

    result.fold(
      (ErrorModel error) {
        emit(state.copyWith(emailSignup: EmailSignupState.error(error)));
        emit(state.copyWith(emailSignup: const EmailSignupState.initial()));
      },
      (UserEntity user) {
        emit(state.copyWith(emailSignup: EmailSignupState.success(user)));
        emit(state.copyWith(emailSignup: const EmailSignupState.initial()));
      },
    );
  }

//   Future<void> signupWithGoogle(AccountType accountType) async {
//     emit(state.copyWith(googleSignup: const GoogleSignupState.initial()));
//     emit(state.copyWith(googleSignup: const GoogleSignupState.loading()));

//     final result = await repository.signupWithGoogle(accountType);

//     result.fold(
//       (error) {
//         emit(state.copyWith(googleSignup: GoogleSignupState.error(error)));
//         emit(state.copyWith(googleSignup: const GoogleSignupState.initial()));
//       },
//       (user) {
//         emit(state.copyWith(googleSignup: GoogleSignupState.success(user)));
//         emit(state.copyWith(googleSignup: const GoogleSignupState.initial()));
//       },
//     );
//   }

//   Future<void> signupWithApple(AccountType accountType) async {
//     emit(state.copyWith(appleSignup: const AppleSignupState.loading()));
//     emit(state.copyWith(appleSignup: const AppleSignupState.initial()));

//     final result = await repository.signupWithApple(accountType);

//     result.fold(
//       (error) {
//         emit(state.copyWith(appleSignup: AppleSignupState.error(error)));
//         emit(state.copyWith(appleSignup: const AppleSignupState.initial()));
//       },
//       (user) {
//         emit(state.copyWith(appleSignup: AppleSignupState.success(user)));
//         emit(state.copyWith(appleSignup: const AppleSignupState.initial()));
//       },
//     );
//   }
}
