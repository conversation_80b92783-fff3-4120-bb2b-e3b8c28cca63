import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';

import '../../../../../app/routes/router.gr.dart';
import '../../../../../core/common/widgets/button_view.dart';
import '../../../../../core/common/widgets/dialogs/custom_dialog_view.dart';
import '../../../../../core/common/widgets/loading_view.dart';
import '../../../../../core/common/widgets/recaptcha_widget.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../../core/enums/auth_field_type.dart';
import '../../../../../injection.dart';
import '../../../data/models/payloads/forgot_password_payload.dart';
import '../../logic/forgot_password_process/forgot_password/forgot_password_cubit.dart';
import '../../logic/forgot_password_process/forgot_password/forgot_password_state.dart';
import '../../logic/forgot_password_process/verify_otp/verify_otp_cubit.dart';
import '../../logic/forgot_password_process/verify_otp/verify_otp_state.dart';

@RoutePage(name: "VerifyOTPPageRoute")
class VerifyOTPPage extends StatelessWidget {
  static const String path = '/verify-otp';

  @visibleForTesting
  final ForgotPasswordPayload payload;

  const VerifyOTPPage({
    super.key,
    required this.payload,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => serviceLocator<VerifyOtpCubit>(),
      child: _VerifyOTPContent(initialPayload: payload),
    );
  }
}

class _VerifyOTPContent extends StatefulWidget {
  final ForgotPasswordPayload initialPayload;

  const _VerifyOTPContent({required this.initialPayload});

  @override
  State<_VerifyOTPContent> createState() => _VerifyOTPContentState();
}

class _VerifyOTPContentState extends State<_VerifyOTPContent> {
  final TextEditingController _otpController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late ForgotPasswordPayload payload;

  @override
  void initState() {
    super.initState();
    payload = widget.initialPayload;
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TextView(text: 'OTP_verification'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextView(
                text: "enter_verification".tr() + payload.credentialValue,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              Pinput(
                length: 6,
                controller: _otpController,
                onTapOutside: (event) => FocusScope.of(context).unfocus(),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                keyboardType: TextInputType.number,
                animationCurve: Curves.bounceInOut,
                animationDuration: const Duration(milliseconds: 300),
                closeKeyboardWhenCompleted: true,
                defaultPinTheme: PinTheme(
                  width: 54,
                  height: 56.0,
                  textStyle: const TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    shape: BoxShape.rectangle,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                      width: 1,
                    ),
                  ),
                ),
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              ),
              const SizedBox(height: 24),
              BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
                listener: (context, state) {
                  state.maybeWhen(
                    orElse: () {},
                    error: (error) {
                      return showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: const TextView(
                              text: "general_error",
                            ),
                            content: TextView(
                              text: error.error?.message ?? "general_error",
                            ),
                            actions: [
                              ButtonView(
                                onClick: () {
                                  Navigator.of(context).pop();
                                },
                                title: "general_OK",
                                buttonType: ButtonType.textButton,
                                semanticLabelValue: "ok button on verify otp page",
                              )
                            ],
                          );
                        },
                      );
                    },
                    success: (token) {
                      // Create a new payload with the second token
                      final updatedPayload = ForgotPasswordPayload(
                        email: payload.email,
                        phone: payload.phone,
                        type: payload.type,
                        token: token, // Use the token from verify OTP response
                      );

                      context.router.navigate(
                        ResetPasswordPageRoute(payload: updatedPayload),
                      );
                    },
                  );
                },
                builder: (context, state) {
                  return state.maybeWhen(
                    orElse: () {
                      return ButtonView(
                        buttonType: ButtonType.solidButton,
                        onClick: () {
                          if (_otpController.text == "") {
                            CustomDialogView.show(
                              context,
                              title: const TextView(text: "empty_pin"),
                              content: const TextView(text: "please_enter_pin_code"),
                              actions: [
                                ButtonView(
                                  title: "general_OK",
                                  onClick: () {
                                    Navigator.of(context).pop();
                                  },
                                  semanticLabelValue: 'OK on empty pin popup',
                                  buttonType: ButtonType.solidButton,
                                ),
                              ],
                            );
                            return;
                          }
                          context.read<VerifyOtpCubit>().verifyOtp(
                                email: payload.type == AuthFieldType.email ? payload.email : null,
                                phone: payload.type == AuthFieldType.phone ? payload.phone : null,
                                identifierType: payload.type,
                                otp: _otpController.text,
                                token: payload.token,
                                recaptchaToken: payload.recaptchaToken,
                              );
                        },
                        semanticLabelValue: 'Verify otp',
                        title: "verify_code".tr(),
                      );
                    },
                    loading: () => const LoadingView(),
                  );
                },
              ),
              const SizedBox(height: 12),
              BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
                listener: (context, state) {
                  state.maybeWhen(
                    success: (payloadFromState, token) {
                      // Create a new payload with the token
                      final updatedPayload = ForgotPasswordPayload(
                        email: payloadFromState.email,
                        phone: payloadFromState.phone,
                        type: payloadFromState.type,
                        token: token,
                        recaptchaToken: payloadFromState.recaptchaToken,
                      );

                      // Update the payload with the token
                      setState(() {
                        payload = updatedPayload;
                      });

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: TextView(text: 'verify_otp_code_has_been_sent'),
                        ),
                      );
                    },
                    orElse: () {},
                  );
                },
                builder: (context, state) {
                  return state.maybeWhen(
                    loading: () => const LoadingView(),
                    orElse: () {
                      return ButtonView(
                        onClick: () async {
                          final recaptchaToken = await showDialog(
                            context: context,
                            barrierDismissible: false,
                            barrierColor: Colors.transparent,
                            builder: (context) {
                              return PopScope(
                                canPop: false,
                                child: SizedBox(
                                  height: 0,
                                  child: RecaptchaWidget(),
                                ),
                              );
                            },
                          );

                          if (!context.mounted) return;

                          // Update payload with new recaptcha token
                          final updatedPayload = ForgotPasswordPayload(
                            email: payload.email,
                            phone: payload.phone,
                            type: payload.type,
                            token: payload.token,
                            recaptchaToken: recaptchaToken,
                          );

                          context.read<ForgotPasswordCubit>().requestForgotPassword(updatedPayload);
                        },
                        title: 'resend_code',
                        buttonType: ButtonType.textButton,
                        semanticLabelValue: "resend button",
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
