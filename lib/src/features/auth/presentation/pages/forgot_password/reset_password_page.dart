import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../app/routes/router.gr.dart';
import '../../../../../app/theme/colors.dart';
import '../../../../../core/common/widgets/button_view.dart';
import '../../../../../core/common/widgets/loading_view.dart';
import '../../../../../core/common/widgets/recaptcha_widget.dart';
import '../../../../../core/common/widgets/text_widgets/text_form_field_view.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../../core/enums/auth_field_type.dart';
import '../../../../../injection.dart';
import '../../../data/models/payloads/forgot_password_payload.dart';
import '../../logic/forgot_password_process/reset_password/reset_password_cubit.dart';
import '../../logic/forgot_password_process/reset_password/reset_password_state.dart';

@RoutePage(name: "ResetPasswordPageRoute")
class ResetPasswordPage extends StatelessWidget {
  static const String path = '/reset-password';

  @visibleForTesting
  final ForgotPasswordPayload payload;

  const ResetPasswordPage({
    super.key,
    required this.payload,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => serviceLocator<ResetPasswordCubit>(),
      child: _ResetPasswordContent(payload: payload),
    );
  }
}

class _ResetPasswordContent extends StatefulWidget {
  final ForgotPasswordPayload payload;

  const _ResetPasswordContent({required this.payload});

  @override
  State<_ResetPasswordContent> createState() => _ResetPasswordContentState();
}

class _ResetPasswordContentState extends State<_ResetPasswordContent> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TextView(text: 'reset_password_title'),
      ),
      body: BlocConsumer<ResetPasswordCubit, ResetPasswordState>(
        listener: (context, state) {
          state.maybeWhen(
            success: () {
              // removedLog log("reset password success");
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  backgroundColor: ColorPalette.progressGreen,
                  content: TextView(
                    text: 'reset_password_successfully',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              );
              context.router.pushAndPopUntil(
                const LoginPageRoute(),
                predicate: (route) => false,
              );
            },
            error: (error) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: TextView(
                    text: '${"reset_password_failed".tr()} ${error.toString()}',
                  ),
                ),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return state.maybeWhen(
            orElse: () {
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const TextView(
                        text: 'reset_passwowrd_create_new_password',
                        style: TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 24),
                      TextFormFieldView(
                        textFormFieldTypes: TextFormFieldTypes.password,
                        controller: _passwordController,
                        hintText: "reset_password_enter_your_new_password",
                        title: "reset_password_new_password",
                        obscureText: _obscurePassword,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormFieldView(
                        textFormFieldTypes: TextFormFieldTypes.password,
                        controller: _confirmPasswordController,
                        hintText: "reset_password_confirm_your_password",
                        title: "reset_password_confirm_password",
                        obscureText: _obscureConfirmPassword,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureConfirmPassword = !_obscureConfirmPassword;
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      ButtonView(
                        buttonType: ButtonType.solidButton,
                        onClick: () async {
                          if (_formKey.currentState!.validate()) {
                            final recaptchaToken = await showDialog(
                              context: context,
                              barrierDismissible: false,
                              barrierColor: Colors.transparent,
                              builder: (context) {
                                return PopScope(
                                  canPop: false,
                                  child: SizedBox(
                                    height: 0,
                                    child: RecaptchaWidget(),
                                  ),
                                );
                              },
                            );

                            if (!context.mounted) return;

                            final identifier = widget.payload.type == AuthFieldType.email ? widget.payload.email! : widget.payload.phone!;

                            context.read<ResetPasswordCubit>().resetPassword(
                                  identifier: identifier,
                                  identifierType: widget.payload.type,
                                  password: _passwordController.text,
                                  token: widget.payload.token,
                                  recaptchaToken: recaptchaToken,
                                );
                          }
                        },
                        title: "reset_password_title",
                        semanticLabelValue: "Reset Password Button",
                      ),
                    ],
                  ),
                ),
              );
            },
            loading: () => const LoadingView(),
          );
        },
      ),
    );
  }
}
