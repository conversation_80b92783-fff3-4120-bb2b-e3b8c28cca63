import 'package:auto_route/auto_route.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../app/routes/router.gr.dart';
import '../../../../../core/common/widgets/button_view.dart';
import '../../../../../core/common/widgets/loading_view.dart';
import '../../../../../core/common/widgets/recaptcha_widget.dart';
import '../../../../../core/common/widgets/text_widgets/text_form_field_view.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../../core/enums/auth_field_type.dart';
import '../../../data/models/payloads/forgot_password_payload.dart';
import '../../logic/forgot_password_process/forgot_password/forgot_password_cubit.dart';
import '../../logic/forgot_password_process/forgot_password/forgot_password_state.dart';

@RoutePage(name: "ForgotPasswordPageRoute")
class ForgotPasswordPage extends StatelessWidget {
  static const String path = '/forgot-password';

  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const _ForgotPasswordContent();
  }
}

class _ForgotPasswordContent extends StatefulWidget {
  const _ForgotPasswordContent();

  @override
  State<_ForgotPasswordContent> createState() => _ForgotPasswordContentState();
}

class _ForgotPasswordContentState extends State<_ForgotPasswordContent> {
  final TextEditingController _credentialController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AuthFieldType authFieldType = AuthFieldType.email;
  String _dialCode = '+964';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _credentialController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TextView(text: 'forgot_password_title'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const TextView(
                text: 'enter_your_email_or_phone_to_receive_a_password_reset_code',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              // Dynamic form field based on selected type
              if (authFieldType == AuthFieldType.email)
                TextFormFieldView(
                  textFormFieldTypes: TextFormFieldTypes.email,
                  hintText: "enter_your_email".tr(),
                  controller: _credentialController,
                  keyboardType: TextInputType.emailAddress,
                )
              else
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(100)),
                      child: CountryCodePicker(
                        onChanged: (CountryCode countryCode) {
                          setState(() {
                            _dialCode = countryCode.dialCode ?? "";
                          });
                        },
                        initialSelection: '+964',
                        favorite: ['+964'],
                        showCountryOnly: false,
                        showOnlyCountryWhenClosed: false,
                        alignLeft: false,
                        showFlag: false,
                        flagWidth: 25,
                        padding: const EdgeInsets.all(8),
                        backgroundColor: Colors.grey[200],
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormFieldView(
                        controller: _credentialController,
                        textFormFieldTypes: TextFormFieldTypes.phone,
                        keyboardType: TextInputType.number,
                        hintText: "enter_your_phone_number".tr(),
                      ),
                    ),
                  ],
                ),
              // Switch button
              Align(
                alignment: Alignment.centerRight,
                child: ButtonView(
                  buttonType: ButtonType.textButton,
                  semanticLabelValue: "phone/email switcher in forgot password page",
                  onClick: () {
                    setState(() {
                      authFieldType = authFieldType == AuthFieldType.email ? AuthFieldType.phone : AuthFieldType.email;
                      // Clear the field when switching
                      _credentialController.clear();
                    });
                  },
                  title: authFieldType == AuthFieldType.email ? "use_phone_number".tr() : "use_email".tr(),
                ),
              ),
              const SizedBox(height: 24),
              BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
                listener: (context, state) {
                  state.maybeWhen(
                    orElse: () {},
                    loading: () {
                      setState(() {
                        _isProcessing = true;
                      });
                    },
                    error: (error) {
                      setState(() {
                        _isProcessing = false;
                      });
                      return showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: const TextView(
                              text: "general_error",
                            ),
                            content: TextView(
                              text: error.error?.message ?? "an_error_occurred".tr(),
                            ),
                            actions: [
                              ButtonView(
                                buttonType: ButtonType.textButton,
                                semanticLabelValue: "press ok button on forgot password dialog",
                                onClick: () {
                                  Navigator.of(context).pop();
                                },
                                title: "general_OK",
                              )
                            ],
                          );
                        },
                      );
                    },
                    success: (payload, token) {
                      setState(() {
                        _isProcessing = false;
                      });
                      // Create a new payload with the token
                      final updatedPayload = ForgotPasswordPayload(
                        email: payload.email,
                        phone: payload.phone,
                        type: payload.type,
                        token: token,
                        recaptchaToken: payload.recaptchaToken,
                      );

                      context.router.navigate(
                        VerifyOTPPageRoute(payload: updatedPayload),
                      );
                    },
                  );
                },
                builder: (context, state) {
                  if (_isProcessing) {
                    return LoadingView();
                  } else {
                    return ButtonView(
                      semanticLabelValue: "Send OTP Code",
                      buttonType: ButtonType.solidButton,
                      onClick: () async {
                        if (!_formKey.currentState!.validate()) return;

                        FocusScope.of(context).requestFocus(FocusNode());

                        setState(() {
                          _isProcessing = true;
                        });

                        final recaptchaToken = await showDialog(
                          context: context,
                          barrierDismissible: false,
                          barrierColor: Colors.transparent,
                          builder: (context) {
                            return PopScope(
                              canPop: false,
                              child: SizedBox(
                                height: 0,
                                child: RecaptchaWidget(),
                              ),
                            );
                          },
                        );

                        if (!context.mounted) return;

                        final ForgotPasswordPayload forgotPasswordPayload = ForgotPasswordPayload(
                          type: authFieldType,
                          email: authFieldType == AuthFieldType.email ? _credentialController.text : null,
                          phone: authFieldType == AuthFieldType.phone ? _dialCode + _credentialController.text : null,
                          recaptchaToken: recaptchaToken,
                        );

                        context.read<ForgotPasswordCubit>().requestForgotPassword(forgotPasswordPayload);
                      },
                      title: "send_otp_code",
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
