import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../app/routes/router.gr.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/common/widgets/app_space_widget.dart';
import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/dialogs/custom_dialog_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/recaptcha_widget.dart';
import '../../../../core/common/widgets/text_widgets/text_form_field_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/enums/auth_field_type.dart';
import '../logic/signup_process/check_exist/check_exist_cubit.dart';
import '../logic/signup_process/check_exist/check_exist_state.dart';
import '../logic/signup_process/registration_otp/registration_otp_cubit.dart';
import '../logic/signup_process/registration_otp/registration_otp_state.dart';
import '../logic/signup_process/signup/signup_cubit.dart';
import '../logic/signup_process/signup/signup_state.dart';

@RoutePage(name: "SignupPageRoute")
class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> with TickerProviderStateMixin {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _identifierController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isProcessing = false;

  // Track whether the input is an email or phone number
  AuthFieldType _identifierType = AuthFieldType.email;
  String _dialCode = '+964';

  @override
  void initState() {
    super.initState();
    // _nameController.text = "Mohammad";
    // _identifierController.text = "<EMAIL>";
    // _passwordController.text = "HHaa1414@";
    // _confirmPasswordController.text = "HHaa1414@";
  }

  @override
  void dispose() {
    _nameController.dispose();
    _identifierController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // AnimatedMeshGradient
  // colors: [
  //   const Color.fromARGB(255, 160, 95, 251),
  //   const Color(0xFFFFFFFF),
  //   const Color(0xFFFFFFFF),
  //   const Color.fromARGB(255, 74, 145, 252),
  // ],
  // options: AnimatedMeshGradientOptions(
  //   speed: 0.5,
  //   grain: 0.18,
  // ),
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Center(
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: .9),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: .1),
                      blurRadius: 10,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  // spacing: 24,
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    TextView(
                      text: "create_account",
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                    ),
                    AppSpacer.p24(),
                    TextFormFieldView(
                      hintText: 'enter_name',
                      textFormFieldTypes: TextFormFieldTypes.requiredText,
                      errorMessage: 'please_enter_your_name',
                      controller: _nameController,
                    ),
                    AppSpacer.p24(),
                    if (_identifierType == AuthFieldType.email)
                      TextFormFieldView(
                        textFormFieldTypes: TextFormFieldTypes.email,
                        hintText: "enter_your_email",
                        controller: _identifierController,
                        keyboardType: TextInputType.emailAddress,
                      )
                    else
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(100),
                            ),
                            child: CountryCodePicker(
                              onChanged: (CountryCode countryCode) {
                                setState(() {
                                  _dialCode = countryCode.dialCode ?? "";
                                });
                              },
                              initialSelection: '+964',
                              favorite: ['+964'],
                              showCountryOnly: false,
                              showOnlyCountryWhenClosed: false,
                              alignLeft: false,
                              showFlag: false,
                              flagWidth: 25,
                              padding: const EdgeInsets.all(8),
                              backgroundColor: Colors.grey[200],
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextFormFieldView(
                              controller: _identifierController,
                              textFormFieldTypes: TextFormFieldTypes.phone,
                              keyboardType: TextInputType.number,
                              hintText: "enter_your_phone_number",
                            ),
                          ),
                        ],
                      ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: ButtonView(
                        buttonType: ButtonType.textButton,
                        semanticLabelValue: "phone/email switcher in signup page",
                        onClick: () {
                          setState(() {
                            _identifierType = _identifierType == AuthFieldType.email ? AuthFieldType.phone : AuthFieldType.email;
                            _identifierController.clear();
                          });
                        },
                        title: _identifierType == AuthFieldType.email ? "use_phone_number" : "use_email",
                      ),
                    ),
                    TextFormFieldView(
                      hintText: 'enter_password',
                      textFormFieldTypes: TextFormFieldTypes.password,
                      controller: _passwordController,
                      obscureText: !_isPasswordVisible,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                    ),
                    AppSpacer.p24(),
                    TextFormFieldView(
                      hintText: 'confirm_password',
                      textFormFieldTypes: TextFormFieldTypes.password,
                      controller: _confirmPasswordController,
                      obscureText: !_isConfirmPasswordVisible,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                          });
                        },
                      ),
                    ),
                    AppSpacer.p24(),
                    MultiBlocListener(
                      listeners: [
                        BlocListener<CheckExistCubit, CheckExistState>(
                          listener: (context, state) {
                            state.maybeWhen(
                              orElse: () {},
                              loading: () {
                                setState(() {
                                  _isProcessing = true;
                                });
                              },
                              error: (ErrorModel error) async {
                                if (error.error?.code == "404") {
                                  final recaptchaToken = await showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    barrierColor: Colors.transparent,
                                    builder: (context) {
                                      return PopScope(
                                        canPop: false,
                                        child: SizedBox(
                                          height: 0,
                                          child: RecaptchaWidget(),
                                        ),
                                      );
                                    },
                                  );

                                  if (!context.mounted) return;

                                  BlocProvider.of<RegistrationOtpCubit>(context).requestRegistrationOtp(
                                    email: _identifierType == AuthFieldType.email ? _identifierController.text.trim() : null,
                                    phone: _identifierType == AuthFieldType.phone ? _dialCode + _identifierController.text.trim() : null,
                                    type: _identifierType,
                                    recaptchaToken: recaptchaToken,
                                  );
                                } else {
                                  setState(() {
                                    _isProcessing = false;
                                  });
                                  inspect(error);
                                  CustomDialogView.show(
                                    context,
                                    title: TextView(text: "general_error"),
                                    content: TextView(
                                      text: error.error?.message ?? 'general_error_occurred',
                                      textAlignment: TextAlign.center,
                                    ),
                                    actions: [
                                      ButtonView(
                                        buttonType: ButtonType.solidButton,
                                        onClick: () {
                                          Navigator.of(context).pop();
                                        },
                                        title: 'general_OK',
                                        semanticLabelValue: 'ok',
                                      ),
                                    ],
                                  );
                                }
                              },
                              success: (bool exists) {
                                if (exists) {
                                  setState(() {
                                    _isProcessing = false;
                                  });

                                  CustomDialogView.show(
                                    context,
                                    title: TextView(text: "account_exists"),
                                    content: TextView(
                                      text: 'account_already_exists',
                                      textAlignment: TextAlign.center,
                                    ),
                                    actions: [
                                      ButtonView(
                                        buttonType: ButtonType.solidButton,
                                        onClick: () {
                                          Navigator.of(context).pop();
                                        },
                                        title: 'general_OK',
                                        semanticLabelValue: 'ok',
                                      ),
                                    ],
                                  );
                                } else {
                                  // Account doesn't exist, proceed with registration OTP
                                  BlocProvider.of<RegistrationOtpCubit>(context).requestRegistrationOtp(
                                    email: _identifierType == AuthFieldType.email ? _identifierController.text.trim() : null,
                                    phone: _identifierType == AuthFieldType.phone ? _dialCode + _identifierController.text.trim() : null,
                                    type: _identifierType,
                                  );
                                }
                              },
                            );
                          },
                        ),
                        BlocListener<RegistrationOtpCubit, RegistrationOtpState>(
                          listener: (context, state) {
                            state.maybeWhen(
                              orElse: () {},
                              loading: () {
                                setState(() {
                                  _isProcessing = true;
                                });
                              },
                              error: (ErrorModel error) {
                                setState(() {
                                  _isProcessing = false;
                                });
                                CustomDialogView.show(
                                  context,
                                  title: TextView(text: "general_error"),
                                  content: TextView(
                                    text: error.error?.message ?? 'general_error_occurred',
                                    textAlignment: TextAlign.center,
                                  ),
                                  actions: [
                                    ButtonView(
                                      buttonType: ButtonType.solidButton,
                                      onClick: () {
                                        Navigator.of(context).pop();
                                      },
                                      title: 'general_OK',
                                      semanticLabelValue: 'ok',
                                    ),
                                  ],
                                );
                              },
                              success: (String token) {
                                setState(() {
                                  _isProcessing = false;
                                });
                                final email = _identifierType == AuthFieldType.email ? _identifierController.text.trim() : _dialCode + _identifierController.text.trim();

                                context.navigateTo(
                                  VerifyOtpPageRoute(
                                    email: email,
                                    name: _nameController.text.trim(),
                                    password: _passwordController.text,
                                    token: token,
                                    type: _identifierType,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        BlocListener<SignupCubit, SignupState>(
                          listener: (context, state) {
                            state.emailSignup.maybeWhen(
                              orElse: () {},
                              loading: () {
                                setState(() {
                                  _isProcessing = true;
                                });
                              },
                              error: (ErrorModel error) {
                                setState(() {
                                  _isProcessing = false;
                                });
                                CustomDialogView.show(
                                  context,
                                  title: TextView(text: "general_error"),
                                  content: TextView(
                                    text: error.error?.message ?? 'general_error_occurred',
                                    textAlignment: TextAlign.center,
                                  ),
                                  actions: [
                                    ButtonView(
                                      buttonType: ButtonType.solidButton,
                                      onClick: () {
                                        Navigator.of(context).pop();
                                      },
                                      title: 'general_OK',
                                      semanticLabelValue: 'ok',
                                    ),
                                  ],
                                );
                              },
                              success: (user) {
                                setState(() {
                                  _isProcessing = false;
                                });
                                context.router.pushAndPopUntil(
                                  const HomePageRoute(),
                                  predicate: (route) => false,
                                );
                              },
                            );
                          },
                        ),
                      ],
                      child: _isProcessing
                          ? const LoadingView()
                          : ButtonView(
                              title: 'sign_up',
                              buttonType: ButtonType.solidButton,
                              onClick: () {
                                if (_confirmPasswordController.text != _passwordController.text) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      backgroundColor: ColorPalette.progressRed,
                                      content: TextView(
                                        text: 'passwords_must_match',
                                      ),
                                    ),
                                  );
                                  return;
                                }

                                if (!_formKey.currentState!.validate()) {
                                  return;
                                }

                                FocusScope.of(context).requestFocus(FocusNode());

                                // First check if the email/phone already exists
                                BlocProvider.of<CheckExistCubit>(context).checkAlreadyExist(
                                  email: _identifierType == AuthFieldType.email ? _identifierController.text.trim() : null,
                                  phone: _identifierType == AuthFieldType.phone ? _dialCode + _identifierController.text.trim() : null,
                                  type: _identifierType,
                                );
                              },
                              semanticLabelValue: 'sign_up',
                            ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextView(
                            text: "already_have_account",
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              context.router.push(
                                const LoginPageRoute(),
                              );
                            },
                            child: TextView(
                              text: "login",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
