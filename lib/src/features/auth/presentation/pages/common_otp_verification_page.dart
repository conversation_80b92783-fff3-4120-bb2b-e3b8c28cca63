import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';

import '../../../../app/routes/router.gr.dart';
import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/dialogs/custom_dialog_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/enums/auth_field_type.dart';
import '../../../../injection.dart';
import '../../data/models/payloads/forgot_password_payload.dart';
import '../../data/models/payloads/otp_verification_data.dart';
import '../logic/forgot_password_process/verify_otp/verify_otp_cubit.dart';
import '../logic/forgot_password_process/verify_otp/verify_otp_state.dart';
import '../logic/signup_process/signup/signup_cubit.dart';

@RoutePage(name: "CommonOtpVerificationPageRoute")
class CommonOtpVerificationPage extends StatelessWidget {
  final OtpVerificationData data;

  const CommonOtpVerificationPage({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => serviceLocator<VerifyOtpCubit>(),
      child: _OtpVerificationContent(data: data),
    );
  }
}

class _OtpVerificationContent extends StatefulWidget {
  final OtpVerificationData data;

  const _OtpVerificationContent({required this.data});

  @override
  State<_OtpVerificationContent> createState() => _OtpVerificationContentState();
}

class _OtpVerificationContentState extends State<_OtpVerificationContent> {
  final TextEditingController _otpController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late OtpVerificationData data;

  @override
  void initState() {
    super.initState();
    data = widget.data;
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TextView(text: 'OTP_verification'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextView(
                text: data.type == AuthFieldType.email ? 'please_check_your_email_for_otp' : 'please_check_your_phone_for_otp',
                style: const TextStyle(fontSize: 16),
                textAlignment: TextAlign.center,
              ),
              const SizedBox(height: 8),
              TextView(
                text: data.type == AuthFieldType.email ? 'email: ${data.email}' : 'phone: ${data.phone}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlignment: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Pinput(
                length: 6,
                controller: _otpController,
                onTapOutside: (event) => FocusScope.of(context).unfocus(),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                keyboardType: TextInputType.number,
                animationCurve: Curves.bounceInOut,
                animationDuration: const Duration(milliseconds: 300),
                closeKeyboardWhenCompleted: true,
                defaultPinTheme: PinTheme(
                  width: 54,
                  height: 56.0,
                  textStyle: const TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    shape: BoxShape.rectangle,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                      width: 1,
                    ),
                  ),
                ),
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              ),
              const SizedBox(height: 24),
              BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
                listener: (context, state) {
                  state.maybeWhen(
                    orElse: () {},
                    error: (error) {
                      CustomDialogView.show(
                        context,
                        title: const TextView(text: "general_error"),
                        content: TextView(
                          text: error.error?.message ?? 'general_error_occurred',
                          textAlignment: TextAlign.center,
                        ),
                        actions: [
                          ButtonView(
                            buttonType: ButtonType.solidButton,
                            onClick: () {
                              Navigator.of(context).pop();
                            },
                            title: 'general_OK',
                            semanticLabelValue: 'ok',
                          ),
                        ],
                      );
                    },
                    success: (token) {
                      if (data.isSignup) {
                        // For signup flow, complete the registration
                        _completeSignup(context, token);
                      } else {
                        // For forgot password flow, navigate to reset password
                        _navigateToResetPassword(context, token);
                      }
                    },
                  );
                },
                builder: (context, state) {
                  return state.maybeWhen(
                    orElse: () {
                      return ButtonView(
                        buttonType: ButtonType.solidButton,
                        onClick: () {
                          if (_otpController.text.isEmpty) {
                            CustomDialogView.show(
                              context,
                              title: const TextView(text: "empty_pin"),
                              content: const TextView(text: "please_enter_pin_code"),
                              actions: [
                                ButtonView(
                                  title: "general_OK",
                                  onClick: () {
                                    Navigator.of(context).pop();
                                  },
                                  semanticLabelValue: 'OK on empty pin popup',
                                  buttonType: ButtonType.solidButton,
                                ),
                              ],
                            );
                            return;
                          }
                          context.read<VerifyOtpCubit>().verifyOtp(
                                email: data.type == AuthFieldType.email ? data.email : null,
                                phone: data.type == AuthFieldType.phone ? data.phone : null,
                                identifierType: data.type,
                                otp: _otpController.text,
                                token: data.token,
                              );
                        },
                        semanticLabelValue: 'Verify otp',
                        title: "verify_code",
                      );
                    },
                    loading: () => const LoadingView(),
                  );
                },
              ),
              const SizedBox(height: 12),
              ButtonView(
                onClick: () {
                  context.router.back();
                },
                buttonType: ButtonType.textButton,
                semanticLabelValue: "resend_code",
                title: 'resend_code',
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _completeSignup(BuildContext context, String token) {
    // Get first and last name from the name field
    final nameParts = data.name?.trim().split(' ') ?? [];
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.last : '';

    // Call signup with the appropriate parameters
    BlocProvider.of<SignupCubit>(context).signup(
      firstName: firstName,
      lastName: lastName,
      email: data.type == AuthFieldType.email ? data.email : null,
      phone: data.type == AuthFieldType.phone ? data.phone : null,
      password: data.password ?? '',
      token: token,
      registerType: data.type,
    );

    // Navigate to home page
    context.router.pushAndPopUntil(
      const HomePageRoute(),
      predicate: (route) => false,
    );
  }

  void _navigateToResetPassword(BuildContext context, String token) {
    // Create a payload for reset password
    final forgotPasswordPayload = ForgotPasswordPayload(
      email: data.email,
      phone: data.phone,
      type: data.type,
      token: token,
    );

    // Navigate to reset password page
    context.router.navigate(
      ResetPasswordPageRoute(payload: forgotPasswordPayload),
    );
  }
}
