import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:country_code_picker/country_code_picker.dart';

import '../../../../app/routes/router.gr.dart';
import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/dialogs/custom_dialog_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_form_field_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/enums/login_type.dart';
import '../../../../injection.dart';
import '../logic/login/login_cubit.dart';
import '../logic/login/login_state.dart';
import '../widgets/social_login_button.dart';
import '../../../../core/common/presentation/logic/user/user_cubit.dart';

@RoutePage(name: "LoginPageRoute")
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final TextEditingController _credentialController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isPasswordVisible = false;
  bool _isEmailLogin = true;
  String _dialCode = "+964";

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _credentialController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleGoogleSignIn(BuildContext context) async {
    try {
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

      final GoogleSignInAuthentication? googleAuth = await googleUser?.authentication;
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth?.accessToken,
        idToken: googleAuth?.idToken,
      );

      final UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      final String? idToken = await userCredential.user?.getIdToken();

      if (!context.mounted) return;

      if (googleUser != null && mounted) {
        context.read<LoginCubit>().loginWithGoogle(token: idToken ?? "");
      }
    } catch (error) {
      // Handle error
      if (mounted) {
        // Determine a more specific error message
        String errorMessage = "error_on_google_signin";
        String detailedError = error.toString();

        if (error is PlatformException) {
          if (error.code == 'sign_in_failed' && error.message?.contains('10:') == true) {
            errorMessage = "google_signin_config_error";
          } else if (error.code == 'network_error') {
            errorMessage = "no_internet_connection_description";
          } else {
            errorMessage = error.message ?? errorMessage;
          }
          detailedError = "Code: ${error.code}, Message: ${error.message}, Details: ${error.details}";
        }

        // Show error dialog
        CustomDialogView.show(
          context,
          title: TextView(text: "Google Sign-In Error"),
          content: TextView(
            text: "${errorMessage.tr()}\n\nDetails: $detailedError",
            textAlignment: TextAlign.center,
          ),
          actions: [
            ButtonView(
              title: 'general_OK',
              buttonType: ButtonType.solidButton,
              onClick: () {
                Navigator.of(context).pop();
              },
              semanticLabelValue: 'OK',
            ),
          ],
        );
      }
    }
  }

  Future<void> _handleAppleSignIn(BuildContext context) async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        rawNonce: appleCredential.state,
        accessToken: appleCredential.authorizationCode,
      );

      final UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(oauthCredential);

      final String? idToken = await userCredential.user?.getIdToken();

      context.read<LoginCubit>().loginWithApple(
            token: idToken ?? "", //todo add token here
          );
    } catch (error) {
      // Handle error
      if (mounted) {
        // Determine a more specific error message
        String errorMessage = "error_on_apple_signin";
        String detailedError = error.toString();

        if (error is SignInWithAppleAuthorizationException) {
          switch (error.code) {
            case AuthorizationErrorCode.canceled:
              errorMessage = "apple_signin_canceled";
              break;
            case AuthorizationErrorCode.failed:
              errorMessage = "apple_signin_failed";
              break;
            case AuthorizationErrorCode.invalidResponse:
              errorMessage = "apple_signin_invalid_response";
              break;
            case AuthorizationErrorCode.notHandled:
              errorMessage = "apple_signin_not_handled";
              break;
            case AuthorizationErrorCode.unknown:
              errorMessage = "apple_signin_unknown_error";
              break;
            default:
              errorMessage = error.message;
          }
          detailedError = "Code: ${error.code}, Message: ${error.message}";
        } else if (error is PlatformException) {
          errorMessage = error.message ?? errorMessage;
          detailedError = "Code: ${error.code}, Message: ${error.message}, Details: ${error.details}";
        }

        // Show error dialog
        CustomDialogView.show(
          context,
          title: TextView(text: "Apple Sign-In Error"),
          content: TextView(
            text: "${errorMessage.tr()}\n\nDetails: $detailedError",
            textAlignment: TextAlign.center,
          ),
          actions: [
            ButtonView(
              title: 'general_OK',
              buttonType: ButtonType.solidButton,
              onClick: () {
                Navigator.of(context).pop();
              },
              semanticLabelValue: 'OK',
            ),
          ],
        );
      }
    }
  }

  // body: AnimatedMeshGradient(
  // colors: [
  //   const Color.fromARGB(255, 160, 95, 251),
  //   const Color(0xFFFFFFFF),
  //   const Color(0xFFFFFFFF),
  //   const Color.fromARGB(255, 74, 145, 252),
  // ],
  // options: AnimatedMeshGradientOptions(
  //   speed: 2,
  //   grain: 0.18,
  // ),

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LoginCubit>(
      create: (context) => LoginCubit(
        repository: serviceLocator(),
      ),
      child: Scaffold(
        body: Container(
          color: Colors.white,
          child: Center(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 32,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      TextView(
                        text: "welcome_back",
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                      ),
                      const SizedBox(height: 24),
                      if (_isEmailLogin)
                        TextFormFieldView(
                          textFormFieldTypes: TextFormFieldTypes.email,
                          hintText: "enter_your_email".tr(),
                          controller: _credentialController,
                          keyboardType: TextInputType.emailAddress,
                        )
                      else
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.outline,
                                    width: 1,
                                  ),
                                  borderRadius: BorderRadius.circular(100)),
                              child: CountryCodePicker(
                                onChanged: (CountryCode countryCode) {
                                  setState(() {
                                    _dialCode = countryCode.dialCode ?? "";
                                  });
                                },
                                initialSelection: '+964',
                                favorite: ['+964'],
                                showCountryOnly: false,
                                showOnlyCountryWhenClosed: false,
                                alignLeft: false,
                                showFlag: false,
                                flagWidth: 25,
                                padding: const EdgeInsets.all(8),
                                backgroundColor: Colors.grey[200],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextFormFieldView(
                                controller: _credentialController,
                                textFormFieldTypes: TextFormFieldTypes.phone,
                                keyboardType: TextInputType.number,
                                hintText: "enter_your_phone_number".tr(),
                              ),
                            ),
                          ],
                        ),
                      // Switch button
                      Align(
                        alignment: Alignment.centerRight,
                        child: ButtonView(
                          buttonType: ButtonType.textButton,
                          semanticLabelValue: "phone/email switcher in login page",
                          onClick: () {
                            setState(() {
                              _isEmailLogin = !_isEmailLogin;
                              // Clear the field when switching
                              _credentialController.clear();
                            });
                          },
                          title: _isEmailLogin ? "use_phone_number".tr() : "use_email".tr(),
                        ),
                      ),
                      TextFormFieldView(
                        hintText: 'enter_password',
                        textFormFieldTypes: TextFormFieldTypes.password,
                        controller: _passwordController,
                        obscureText: !_isPasswordVisible,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                          ),
                          onPressed: () {
                            setState(() {
                              _isPasswordVisible = !_isPasswordVisible;
                            });
                          },
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ButtonView(
                            onClick: () {
                              context.router.push(const ForgotPasswordPageRoute());
                            },
                            buttonType: ButtonType.textButton,
                            semanticLabelValue: "forgot password button",
                            title: "forgot_password",
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      BlocConsumer<LoginCubit, LoginState>(
                        listener: (context, state) {
                          state.emailLogin.maybeWhen(
                            orElse: () {},
                            error: (ErrorModel error) {
                              CustomDialogView.show(
                                context,
                                title: TextView(text: "general_error"),
                                content: TextView(
                                  text: error.error?.message ?? 'an_error_occurred',
                                  textAlignment: TextAlign.center,
                                ),
                                actions: [
                                  ButtonView(
                                    buttonType: ButtonType.solidButton,
                                    onClick: () {
                                      Navigator.of(context).pop();
                                    },
                                    title: 'general_OK',
                                    semanticLabelValue: '',
                                  ),
                                ],
                              );
                            },
                            success: (user) {
                              context.read<UserCubit>().setUser(user);
                              context.router.pushAndPopUntil(
                                const HomePageRoute(),
                                predicate: (route) => false,
                              );
                            },
                          );
                        },
                        builder: (context, state) {
                          return state.emailLogin.maybeWhen(
                            orElse: () => ButtonView(
                              title: 'login',
                              buttonType: ButtonType.solidButton,
                              onClick: () {
                                if (!_formKey.currentState!.validate()) {
                                  return;
                                }
                                BlocProvider.of<LoginCubit>(context).login(
                                  email: _isEmailLogin ? _credentialController.text : _dialCode + _credentialController.text,
                                  password: _passwordController.text,
                                  loginType: _isEmailLogin ? LoginType.email : LoginType.phone,
                                );
                              },
                              semanticLabelValue: 'login',
                            ),
                            loading: () => const LoadingView(),
                            success: (user) => const SizedBox.shrink(),
                            error: (error) => const SizedBox.shrink(),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          Expanded(
                            child: Divider(
                              color: Colors.grey.shade400,
                              thickness: 1,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: TextView(
                              text: "or",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                            ),
                          ),
                          Expanded(
                            child: Divider(
                              color: Colors.grey.shade400,
                              thickness: 1,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      BlocConsumer<LoginCubit, LoginState>(
                        listenWhen: (previous, current) => previous.googleLogin != current.googleLogin,
                        listener: (context, state) {
                          state.googleLogin.maybeWhen(
                            orElse: () {},
                            error: (ErrorModel error) {
                              CustomDialogView.show(
                                context,
                                title: TextView(text: "google_login_error"),
                                content: TextView(
                                  text: error.error?.message ?? 'an_error_occurred',
                                  textAlignment: TextAlign.center,
                                ),
                                actions: [
                                  ButtonView(
                                    title: 'general_OK',
                                    buttonType: ButtonType.solidButton,
                                    onClick: () {
                                      Navigator.of(context).pop();
                                    },
                                    semanticLabelValue: 'OK',
                                  ),
                                ],
                              );
                            },
                            success: (user) {
                              context.read<UserCubit>().setUser(user);
                              context.router.replace(const HomePageRoute());
                            },
                          );
                        },
                        builder: (context, state) {
                          final isLoading = state.googleLogin.maybeWhen(
                            orElse: () => false,
                            loading: () => true,
                          );

                          return SocialLoginButton(
                            type: SocialLoginType.google,
                            isLoading: isLoading,
                            onPressed: () => _handleGoogleSignIn(context),
                          );
                        },
                      ),

                      // Only show Apple Sign-in button on iOS
                      if (defaultTargetPlatform == TargetPlatform.iOS) ...[
                        const SizedBox(height: 16),
                        BlocConsumer<LoginCubit, LoginState>(
                          listenWhen: (previous, current) => previous.appleLogin != current.appleLogin,
                          listener: (context, state) {
                            state.appleLogin.maybeWhen(
                              orElse: () {},
                              error: (ErrorModel error) {
                                CustomDialogView.show(
                                  context,
                                  title: TextView(text: "apple_login_error"),
                                  content: TextView(
                                    text: error.error?.message ?? 'an_error_occurred',
                                    textAlignment: TextAlign.center,
                                  ),
                                  actions: [
                                    ButtonView(
                                      title: 'general_OK',
                                      buttonType: ButtonType.solidButton,
                                      onClick: () {
                                        Navigator.of(context).pop();
                                      },
                                      semanticLabelValue: 'OK',
                                    ),
                                  ],
                                );
                              },
                              success: (user) {
                                context.read<UserCubit>().setUser(user);
                                context.router.replace(const HomePageRoute());
                              },
                            );
                          },
                          builder: (context, state) {
                            final isLoading = state.appleLogin.maybeWhen(
                              orElse: () => false,
                              loading: () => true,
                            );

                            return SocialLoginButton(
                              type: SocialLoginType.apple,
                              isLoading: isLoading,
                              onPressed: () => _handleAppleSignIn(context),
                            );
                          },
                        ),
                      ],

                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextView(
                            text: "not_registered",
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          CupertinoButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              // Use the new registration flow instead of the old signup page
                              context.router.push(const SignupPageRoute());
                            },
                            child: TextView(
                              text: "create_account",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
