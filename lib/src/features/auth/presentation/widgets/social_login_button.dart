import 'package:flutter/material.dart';

import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';

enum SocialLoginType { google, apple }

class SocialLoginButton extends StatelessWidget {
  final SocialLoginType type;
  final VoidCallback onPressed;
  final bool isLoading;

  const SocialLoginButton({
    super.key,
    required this.type,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: isLoading
            ? LoadingView()
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _getIcon(),
                  const SizedBox(width: 12),
                  TextView(
                    text: _getButtonText(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _getIcon() {
    switch (type) {
      case SocialLoginType.google:
        return Container(
          width: 24,
          height: 24,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          child: Image.asset(
            'assets/images/icons/google.png',
            width: 24,
            height: 24,
            fit: BoxFit.contain,
          ),
        );
      case SocialLoginType.apple:
        return const Icon(
          Icons.apple,
          size: 24,
          color: Colors.black,
        );
    }
  }

  String _getButtonText() {
    switch (type) {
      case SocialLoginType.google:
        return 'continue_with_google';
      case SocialLoginType.apple:
        return 'continue_with_apple';
    }
  }
}
