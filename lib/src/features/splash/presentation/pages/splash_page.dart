import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../../../app/routes/router.gr.dart';
import '../../../../app/theme/colors.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../core/enums/secure_storage_key.dart';
import '../../../../core/utils/managers/database/database_manager.dart';
import '../../../../injection.dart';

@RoutePage(name: "SplashPageRoute")
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _checkAuthAndNavigate();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkAuthAndNavigate() async {
    if (!mounted) return;

    final String? token = await serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token);

    final bool isAuthenticated = token != null && token.isNotEmpty;

    if (!mounted) return;

    if (isAuthenticated) {
      context.router.replace(const HomePageRoute());
    } else {
      context.router.replace(const LoginPageRoute());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo with animation
            // LogoView(
            //   logoSize: 150,
            //   logoColor: ColorPalette.primaryColor,
            // ),
            const SizedBox(height: 40),
            // Custom loading animation
            LoadingView(
              loadingSize: 50,
              loadingColor: ColorPalette.primaryColor,
            ),
            const TextView(
              text: "loading_screen_loading_text",
              style: TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
