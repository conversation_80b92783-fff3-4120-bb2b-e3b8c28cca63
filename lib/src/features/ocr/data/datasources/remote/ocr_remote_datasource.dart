import 'dart:io';
import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/enums/secure_storage_key.dart';
import '../../../../../core/utils/managers/database/database_manager.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../../../../injection.dart';
import '../../models/ocr_response_model.dart';

abstract class OcrRemoteDataSource {
  Future<OcrResponseModel> extractTextFromImage({
    required String imagePath,
  });
}

class OcrRemoteDataSourceImpl implements OcrRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  OcrRemoteDataSourceImpl({required this.httpManager, required this.aesEncryptionManager});

  @override
  Future<OcrResponseModel> extractTextFromImage({
    required String imagePath,
  }) async {
    try {
      // Create a file object to make sure it exists
      final File file = File(imagePath);
      if (!await file.exists()) {
        throw ErrorModel(error: Error(message: 'File not found: $imagePath'));
      }

      // Get file name from path
      final String fileName = imagePath.split('/').last;

      // Create Dio with appropriate timeouts
      final dio = Dio();
      dio.options.connectTimeout = const Duration(minutes: 2);
      dio.options.receiveTimeout = const Duration(minutes: 5);
      dio.options.sendTimeout = const Duration(minutes: 2);

      // Create multipart file
      final MultipartFile multipartFile = await MultipartFile.fromFile(
        imagePath,
        filename: fileName,
      );

      // Create FormData with the required fields
      final formData = FormData.fromMap({
        'file': multipartFile,
      });

      final String? token = await serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token);

      // Add required headers
      final headers = {
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data',
        "Authorization": "Bearer $token",
      };

      // Make the API request
      final response = await dio.post(
        Api().ocrToText,
        data: formData,
        options: Options(
          headers: headers,
          validateStatus: (status) => true,
        ),
      );

      // Handle response
      if (response.statusCode == 200) {
        try {
          final String decryptedData = await aesEncryptionManager.decryptData(response.data["data"]);

          Map<String, dynamic> decodedData = json.decode(decryptedData) as Map<String, dynamic>;

          return OcrResponseModel.fromJson(decodedData);
        } catch (e) {
          if (e is ErrorModel) rethrow;
          throw ErrorModel(
            error: Error(message: 'Failed to parse OCR response: ${e.toString()}'),
          );
        }
      } else {
        String errorMessage = 'Server error';
        if (response.data is Map) {
          errorMessage = response.data['message'] ?? 'Unknown server error';
        } else if (response.data is String) {
          errorMessage = response.data;
        }
        throw ErrorModel(
          error: Error(message: 'Error ${response.statusCode}: $errorMessage'),
        );
      }
    } catch (e) {
      if (e is ErrorModel) {
        rethrow;
      }

      if (e is DioException && e.type == DioExceptionType.receiveTimeout) {
        throw ErrorModel(
          error: Error(
            message: 'The OCR text extraction is taking longer than expected. Please try a smaller image or try again later.',
          ),
        );
      }

      throw ErrorModel(error: Error(message: e.toString()));
    }
  }
}
