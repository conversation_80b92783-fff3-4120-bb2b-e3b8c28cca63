import 'package:dartz/dartz.dart';

import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/utils/helpers/error_parser.dart';
import '../../domain/entities/ocr_entity.dart';
import '../../domain/repositories/ocr_repository.dart';
import '../datasources/remote/ocr_remote_datasource.dart';
import '../models/ocr_response_model.dart';

class OcrRepositoryImpl implements OcrRepository {
  final OcrRemoteDataSource _remoteDataSource;

  OcrRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<ErrorModel, OcrEntity>> extractTextFromImage({required String imagePath}) async {
    try {
      final OcrResponseModel result = await _remoteDataSource.extractTextFromImage(imagePath: imagePath);

      return Right(result.toEntity());
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
