import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import 'package:permission_handler/permission_handler.dart';

import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../ocr/domain/entities/ocr_entity.dart';
import '../logic/ocr/ocr_cubit.dart';

class OcrPage extends StatefulWidget {
  const OcrPage({super.key});

  @override
  State<OcrPage> createState() => _OcrPageState();
}

class _OcrPageState extends State<OcrPage> with AutomaticKeepAliveClientMixin {
  String? _selectedImagePath;
  String? _extractedText;
  final ImagePicker _picker = ImagePicker();

  @override
  bool get wantKeepAlive => true;

  Future<void> _showImageSourceOptions() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextView(
                text: 'ocr_page_select_image_source',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildModalOptionButton(
                    icon: Icons.camera_alt,
                    label: 'ocr_page_camera'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      _checkCameraPermissionAndTakePicture();
                    },
                  ),
                  _buildModalOptionButton(
                    icon: Icons.photo_library,
                    label: 'ocr_page_gallery'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      _checkGalleryPermissionAndPickImage();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModalOptionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 30,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          TextView(
            text: label,
            style: Theme.of(context).textTheme.titleSmall,
          ),
        ],
      ),
    );
  }

  Future<void> _checkGalleryPermissionAndPickImage() async {
    // For Android
    if (Platform.isAndroid) {
      // Check Android version to determine which permissions to request
      if (await _isAndroid13OrHigher()) {
        // For Android 13+, request READ_MEDIA_IMAGES permission
        final PermissionStatus imagesStatus = await Permission.photos.status;

        if (imagesStatus.isGranted) {
          await _pickImage();
        } else if (imagesStatus.isDenied) {
          final PermissionStatus requestStatus = await Permission.photos.request();
          if (requestStatus.isGranted) {
            await _pickImage();
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: TextView(
                    text: 'ocr_page_storage_permission_required',
                  ),
                ),
              );
            }
          }
        } else if (imagesStatus.isPermanentlyDenied) {
          if (mounted) {
            _showPermissionDeniedDialog('photos');
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: TextView(text: 'ocr_page_storage_permission_required')),
            );
          }
        }
      } else {
        // For Android 12 and below, use storage permission
        final PermissionStatus storageStatus = await Permission.storage.status;

        if (storageStatus.isGranted) {
          await _pickImage();
        } else if (storageStatus.isDenied) {
          final PermissionStatus requestStatus = await Permission.storage.request();
          if (requestStatus.isGranted) {
            await _pickImage();
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: TextView(
                    text: 'ocr_page_storage_permission_required',
                  ),
                ),
              );
            }
          }
        } else if (storageStatus.isPermanentlyDenied) {
          if (mounted) {
            _showPermissionDeniedDialog('storage');
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: TextView(text: 'ocr_page_storage_permission_required')),
            );
          }
        }
      }
    }
    // For iOS
    else if (Platform.isIOS) {
      final PermissionStatus status = await Permission.photos.status;

      if (status.isGranted) {
        await _pickImage();
      } else if (status.isDenied) {
        final PermissionStatus requestStatus = await Permission.photos.request();
        if (requestStatus.isGranted) {
          await _pickImage();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: TextView(text: 'ocr_page_photos_permission_required'),
              ),
            );
          }
        }
      } else if (status.isPermanentlyDenied) {
        if (mounted) {
          _showPermissionDeniedDialog('gallery');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: TextView(text: 'ocr_page_photos_permission_required'.tr())),
          );
        }
      }
    }
  }

  // Helper method to check if device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 is API level 33
    }
    return false;
  }

  Future<void> _checkCameraPermissionAndTakePicture() async {
    final PermissionStatus cameraStatus = await Permission.camera.status;

    // Camera permission is required for both platforms
    if (cameraStatus.isGranted) {
      // On Android, we also need storage permission to save the picture
      if (Platform.isAndroid) {
        if (await _isAndroid13OrHigher()) {
          // For Android 13+, check photos permission
          final PermissionStatus photosStatus = await Permission.photos.status;
          if (!photosStatus.isGranted) {
            final PermissionStatus requestPhotos = await Permission.photos.request();
            if (!requestPhotos.isGranted) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: TextView(text: 'ocr_page_storage_permission_required')),
                );
              }
              return;
            }
          }
        } else {
          // For Android 12 and below, check storage permission
          final PermissionStatus storageStatus = await Permission.storage.status;
          if (!storageStatus.isGranted) {
            final PermissionStatus requestStorage = await Permission.storage.request();
            if (!requestStorage.isGranted) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: TextView(text: 'ocr_page_storage_permission_required')),
                );
              }
              return;
            }
          }
        }
      }

      await _takePicture();
    } else if (cameraStatus.isDenied) {
      final PermissionStatus requestStatus = await Permission.camera.request();
      if (requestStatus.isGranted) {
        // Check for storage permission on Android
        if (Platform.isAndroid) {
          if (await _isAndroid13OrHigher()) {
            // For Android 13+, check photos permission
            final PermissionStatus photosStatus = await Permission.photos.status;
            if (!photosStatus.isGranted) {
              final PermissionStatus requestPhotos = await Permission.photos.request();
              if (!requestPhotos.isGranted) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: TextView(text: 'ocr_page_storage_permission_required'),
                    ),
                  );
                }
                return;
              }
            }
          } else {
            // For Android 12 and below, check storage permission
            final PermissionStatus storageStatus = await Permission.storage.status;
            if (!storageStatus.isGranted) {
              final PermissionStatus requestStorage = await Permission.storage.request();
              if (!requestStorage.isGranted) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: TextView(text: 'ocr_page_storage_permission_required'),
                    ),
                  );
                }
                return;
              }
            }
          }
        }

        await _takePicture();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: TextView(text: 'ocr_page_camera_permission_required'.tr())),
          );
        }
      }
    } else if (cameraStatus.isPermanentlyDenied) {
      if (mounted) {
        _showPermissionDeniedDialog('camera');
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: TextView(text: 'ocr_page_camera_permission_required'.tr())),
        );
      }
    }
  }

  void _showPermissionDeniedDialog(String permissionType) {
    String title = 'ocr_page_permission_required';
    String message = '';

    if (permissionType == 'camera') {
      message = 'ocr_page_camera_permission_required';
    } else if (permissionType == 'gallery') {
      message = 'ocr_page_photos_permission_required';
    } else if (permissionType == 'storage') {
      message = 'ocr_page_storage_permission_required';
    } else {
      message = 'Please enable $permissionType access in your device settings to use this feature.';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: TextView(text: title),
        content: TextView(text: message),
        actions: [
          ButtonView(
            semanticLabelValue: "cancel",
            buttonType: ButtonType.textButton,
            onClick: () => Navigator.pop(context),
            title: "general_cancel",
          ),
          ButtonView(
            semanticLabelValue: "cancel",
            buttonType: ButtonType.textButton,
            onClick: () {
              Navigator.pop(context);
              openAppSettings();
            },
            title: "ocr_page_open_settings",
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      // Double-check permission as a safety measure
      if (Platform.isAndroid) {
        if (await _isAndroid13OrHigher()) {
          // For Android 13+, check photos permission
          if (!(await Permission.photos.isGranted)) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: TextView(text: 'ocr_page_photos_permission_not_granted')),
              );
            }
            return;
          }
        } else {
          // For Android 12 and below, check storage permission
          if (!(await Permission.storage.isGranted)) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: TextView(text: 'ocr_page_storage_permission_not_granted')),
              );
            }
            return;
          }
        }
      } else if (Platform.isIOS) {
        if (!(await Permission.photos.isGranted)) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: TextView(text: 'ocr_page_photos_permission_not_granted')),
            );
          }
          return;
        }
      }

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImagePath = pickedFile.path;
          _extractedText = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: TextView(
              text: '${"ocr_page_error_picking_image".tr()} ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  Future<void> _takePicture() async {
    try {
      // Double-check camera permission as a safety measure
      if (!(await Permission.camera.isGranted)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: TextView(
                text: 'ocr_page_camera_permission_not_granted'.tr(),
              ),
            ),
          );
        }
        return;
      }

      // On Android, we also need storage/photos permission
      if (Platform.isAndroid) {
        if (await _isAndroid13OrHigher()) {
          // For Android 13+, check photos permission
          if (!(await Permission.photos.isGranted)) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: TextView(text: 'ocr_page_photos_permission_not_granted'),
                ),
              );
            }
            return;
          }
        } else {
          // For Android 12 and below, check storage permission
          if (!(await Permission.storage.isGranted)) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: TextView(text: 'ocr_page_storage_permission_not_granted'),
                ),
              );
            }
            return;
          }
        }
      }

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImagePath = pickedFile.path;
          _extractedText = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: TextView(
              text: '${"orc_page_error_taking_picture".tr()} ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  void _extractText() {
    if (_selectedImagePath == null) return;

    context.read<OcrCubit>().extractTextFromImage(
          imagePath: _selectedImagePath!,
        );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<OcrCubit, OcrState>(
      listener: (context, state) {
        state.maybeWhen(
          success: (data) {
            setState(() {
              _extractedText = data.text;
            });
          },
          error: (message) {
            // removedLog log(message.toString());
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: TextView(text: message)),
            );
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        return Column(
          children: [
            Expanded(
              child: state.maybeWhen(
                loading: () => const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      LoadingView(),
                      SizedBox(height: 20),
                      TextView(
                        text: 'ocr_page_extracting_text_from_image_description',
                      ),
                      SizedBox(height: 8),
                      TextView(
                        text: '',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                success: (OcrEntity data) {
                  return _buildResultContent();
                },
                error: (_) => _buildInitialContent(),
                initial: () => _buildInitialContent(),
                orElse: () => _buildInitialContent(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInitialContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Center(
              child: Column(
                children: [
                  TextView(
                    text: 'ocr_page_title',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  TextView(
                    text: 'ocr_page_description',
                    // textAlignment: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),
            _buildImageSelector(),
            if (_selectedImagePath != null) ...[
              const SizedBox(height: 20),
              ButtonView(
                buttonType: ButtonType.solidButton,
                semanticLabelValue: 'Extract Text',
                onClick: _extractText,
                title: 'ocr_page_extract_text',
                isDisabled: false,
                isExpanded: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildImageSelector() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: _selectedImagePath == null ? _showImageSourceOptions : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Colors.grey.withValues(alpha: .3)),
        ),
        child: _selectedImagePath == null ? _buildSelectImageContent() : _buildSelectedImageContent(),
      ),
    );
  }

  Widget _buildSelectImageContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: Column(
        children: [
          Icon(
            Icons.image_search,
            size: 48,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 16),
          TextView(
            text: 'ocr_page_select_image',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
            textAlignment: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedImageContent() {
    return Column(
      children: [
        Stack(
          alignment: Alignment.topRight,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.file(
                File(_selectedImagePath!),
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                setState(() {
                  _selectedImagePath = null;
                  _extractedText = null;
                });
                context.read<OcrCubit>().reset();
              },
              child: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.7),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 20,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextView(
          text: 'ocr_page_image_selected',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
      ],
    );
  }

  Widget _buildResultContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Success indicator
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.shade200,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green.shade700,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextView(
                      text: 'ocr_page_text_extracted_successfully',
                      style: TextStyle(
                        color: Colors.green.shade800,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Display the processed image
            if (_selectedImagePath != null) ...[
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.withValues(alpha: 0.3),
                  ),
                  color: Colors.grey[50],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextView(
                              text: 'ocr_page_processed_image',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                          Icon(
                            Icons.zoom_in,
                            size: 20,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          TextView(
                            text: 'ocr_page_tap_to_enlarge',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => _openFullScreenImage(_selectedImagePath!),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        child: Image.file(
                          File(_selectedImagePath!),
                          height: 200,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            Row(
              children: [
                Expanded(
                  child: TextView(
                    text: 'ocr_page_extracted_text',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () {
                    if (_extractedText != null && _extractedText!.isNotEmpty) {
                      Clipboard.setData(ClipboardData(text: _extractedText!));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: TextView(text: 'ocr_page_text_copied_to_clipboard'),
                        ),
                      );
                    }
                  },
                  tooltip: 'ocr_page_copy_to_clipboard',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: TextView(
                text: _extractedText ?? '',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontFamily: 'Rabar015',
                    ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: ButtonView(
                buttonType: ButtonType.textButton,
                semanticLabelValue: 'Try another image',
                onClick: () {
                  setState(() {
                    _selectedImagePath = null;
                    _extractedText = null;
                  });
                  context.read<OcrCubit>().reset();
                },
                title: 'ocr_page_try_another_image',
                buttonTextColor: Theme.of(context).primaryColor,
                isExpanded: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullScreenImage(String imagePath) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            elevation: 0,
          ),
          body: Center(
            child: InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.file(
                File(imagePath),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
