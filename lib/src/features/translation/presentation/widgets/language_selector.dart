import 'package:defer_pointer/defer_pointer.dart';
import 'package:dropdown_flutter/custom_dropdown.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

class LanguageSelector extends StatelessWidget {
  final String sourceLanguage;
  final String targetLanguage;
  final List<String> languages;
  final Function(String?) onSourceLanguageChanged;
  final Function(String?) onTargetLanguageChanged;
  final VoidCallback onSwap;

  const LanguageSelector({
    super.key,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.languages,
    required this.onSourceLanguageChanged,
    required this.onTargetLanguageChanged,
    required this.onSwap,
  });

  @override
  Widget build(BuildContext context) {
    final deferredPointerLink = DeferredPointerHandlerLink();

    const double buttonSize = 48.0;
    const double containerHeight = 58.0;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return DeferredPointerHandler(
      link: deferredPointerLink,
      child: SizedBox(
        height: containerHeight,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            CustomPaint(
              painter: NotchedContainerPainter(
                color: Colors.white,
                shadowColor: Colors.grey.withValues(alpha: 0.15),
                buttonRadius: buttonSize / 2,
              ),
              child: SizedBox(
                height: containerHeight,
                child: Row(
                  children: [
                    Directionality(
                      textDirection: context.locale.languageCode == 'en' ? ui.TextDirection.rtl : ui.TextDirection.ltr,
                      child: Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: DropdownFlutter<String>(
                            hintText: 'Source',
                            items: languages,
                            initialItem: sourceLanguage,
                            onChanged: onSourceLanguageChanged,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: buttonSize + 8),
                    Expanded(
                      child: Directionality(
                        textDirection: context.locale.languageCode == 'en' ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: DropdownFlutter<String>(
                            hintText: 'Target',
                            items: languages,
                            initialItem: targetLanguage,
                            onChanged: onTargetLanguageChanged,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: -28,
              child: DeferPointer(
                link: deferredPointerLink,
                child: ScaleButton(
                  onPressed: onSwap,
                  child: Container(
                    width: buttonSize,
                    height: buttonSize,
                    decoration: BoxDecoration(
                      color: primaryColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    padding: EdgeInsets.zero,
                    child: Icon(
                      Icons.swap_horiz,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NotchedContainerPainter extends CustomPainter {
  final Color color;
  final Color shadowColor;
  final double buttonRadius;

  NotchedContainerPainter({
    required this.color,
    required this.shadowColor,
    required this.buttonRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final height = size.height;
    final width = size.width;
    final centerX = width / 2;

    // Create the path for the container with notch
    final path = Path();

    // Start from the top-left corner
    path.moveTo(20, 0);

    // Top-left to top-center (left of notch)
    path.lineTo(centerX - buttonRadius - 5, 0);

    // Create the concave curve for the notch
    // This creates a smooth curve that matches the button's circular shape
    path.quadraticBezierTo(
      centerX - buttonRadius + 2, // Control point X
      buttonRadius - 2, // Control point Y
      centerX, // End point X
      buttonRadius, // End point Y
    );

    // Create the right side of the notch
    path.quadraticBezierTo(
      centerX + buttonRadius - 2, // Control point X
      buttonRadius - 2, // Control point Y
      centerX + buttonRadius + 5, // End point X
      0, // End point Y
    );

    // Continue to the top-right corner
    path.lineTo(width - 20, 0);

    // Top-right corner radius
    path.quadraticBezierTo(width, 0, width, 20);

    // Right side
    path.lineTo(width, height - 20);

    // Bottom-right corner radius
    path.quadraticBezierTo(width, height, width - 20, height);

    // Bottom side
    path.lineTo(20, height);

    // Bottom-left corner radius
    path.quadraticBezierTo(0, height, 0, height - 20);

    // Left side
    path.lineTo(0, 20);

    // Top-left corner radius
    path.quadraticBezierTo(0, 0, 20, 0);

    // Close the path
    path.close();

    // Draw shadow
    final shadowPaint = Paint()
      ..color = shadowColor
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawPath(path, shadowPaint);

    // Draw the main container
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ScaleButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double pressedScale;
  final Duration duration;

  const ScaleButton({
    super.key,
    required this.child,
    this.onPressed,
    this.pressedScale = 0.9,
    this.duration = const Duration(milliseconds: 100),
  });

  @override
  State<ScaleButton> createState() => _ScaleButtonState();
}

class _ScaleButtonState extends State<ScaleButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (_) {
        if (widget.onPressed != null) {
          setState(() => _isPressed = true);
        }
      },
      onTapUp: (_) {
        if (widget.onPressed != null) {
          setState(() => _isPressed = false);
          widget.onPressed!();
        }
      },
      onTapCancel: () {
        if (widget.onPressed != null) {
          setState(() => _isPressed = false);
        }
      },
      child: AnimatedScale(
        scale: _isPressed ? widget.pressedScale : 1.0,
        curve: Curves.easeOut,
        duration: widget.duration,
        child: widget.child,
      ),
    );
  }
}
