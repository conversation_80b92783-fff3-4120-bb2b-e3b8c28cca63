import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart' show AudioEncoder, AudioRecorder, RecordConfig;

import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../domain/repositories/speech_to_text_repository.dart';
import 'speech_to_text_state.dart';

class SpeechToTextCubit extends Cubit<SpeechToTextState> {
  final SpeechToTextRepository _repository;
  final _audioRecorder = AudioRecorder();
  String? _recordingPath;
  Timer? _recordingTimer;
  bool _isRecording = false;

  static const int _maxRecordingDuration = 30;

  SpeechToTextCubit({
    required SpeechToTextRepository repository,
  })  : _repository = repository,
        super(const SpeechToTextInitial());

  // Helper method to check if device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 is API level 33
    }
    return false;
  }

  Future<void> startRecording() async {
    try {
      // Check if already recording
      if (_isRecording) {
        return;
      }

      // Request microphone permission
      final micStatus = await Permission.microphone.request();
      if (micStatus != PermissionStatus.granted) {
        emit(const SpeechToTextPermissionDenied());
        return;
      }

      // For Android 13+, also request audio permission
      if (Platform.isAndroid && await _isAndroid13OrHigher()) {
        final audioStatus = await Permission.audio.request();
        if (audioStatus != PermissionStatus.granted) {
          emit(const SpeechToTextPermissionDenied());
          return;
        }
      }

      // Update state to listening
      emit(const SpeechToTextListening());

      // Create a temporary file path for the recording
      final tempDir = Directory.systemTemp;
      _recordingPath = '${tempDir.path}/speech_to_text_${DateTime.now().millisecondsSinceEpoch}.wav';

      // Start recording with WAV format (PCM 16bit)
      await _audioRecorder.start(
        path: _recordingPath ?? "",
        RecordConfig(
          encoder: AudioEncoder.wav,
          bitRate: 128000, // 128 kbps
          sampleRate: 44100, // 44.1 kHz
        ),
      );
      _isRecording = true;

      // Set a timer for maximum recording duration
      _recordingTimer = Timer(Duration(seconds: _maxRecordingDuration), () {
        if (_isRecording && state is SpeechToTextListening) {
          _stopRecording();
        }
      });
    } catch (e) {
      _isRecording = false;
      emit(SpeechToTextError(ErrorModel.fromException(e)));
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    try {
      // Cancel the timer if it's active
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // Stop the recording
      await _audioRecorder.stop();
      _isRecording = false;
    } catch (e) {
      _isRecording = false;
    }
  }

  Future<void> stopRecordingAndConvertKurdish({required bool isKurmanji}) async {
    if (_recordingPath == null) return;

    try {
      emit(const SpeechToTextLoading());

      // Make sure recording is stopped
      if (_isRecording) {
        await _stopRecording();
      }

      // Check if the file exists and has content
      final file = File(_recordingPath!);
      if (!file.existsSync() || await file.length() == 0) {
        emit(SpeechToTextError(ErrorModel(
          error: Error(message: 'No audio recorded or recording too short'),
        )));
        _cleanupRecording();
        return;
      }

      final result = await _repository.convertKurdishSpeechToText(
        audioFilePath: _recordingPath!,
        isKurmanji: isKurmanji,
      );

      result.fold(
        (error) => emit(SpeechToTextError(error)),
        (data) => emit(SpeechToTextSuccess(data)),
      );

      _cleanupRecording();
    } catch (e) {
      emit(SpeechToTextError(ErrorModel.fromException(e)));
      _cleanupRecording();
    }
  }

  Future<void> stopRecordingAndConvertOther({required String languageCode}) async {
    if (_recordingPath == null) return;

    try {
      emit(const SpeechToTextLoading());

      // Make sure recording is stopped
      if (_isRecording) {
        await _stopRecording();
      }

      // Check if the file exists and has content
      final file = File(_recordingPath!);
      if (!file.existsSync() || await file.length() == 0) {
        emit(SpeechToTextError(ErrorModel(
          error: Error(message: 'No audio recorded or recording too short'),
        )));
        _cleanupRecording();
        return;
      }

      final result = await _repository.convertOtherLanguageSpeechToText(
        audioFilePath: _recordingPath!,
        languageCode: languageCode,
      );

      result.fold(
        (error) => emit(SpeechToTextError(error)),
        (data) => emit(SpeechToTextSuccess(data)),
      );

      _cleanupRecording();
    } catch (e) {
      emit(SpeechToTextError(ErrorModel.fromException(e)));
      _cleanupRecording();
    }
  }

  void _cleanupRecording() {
    try {
      if (_recordingPath != null) {
        final file = File(_recordingPath!);
        if (file.existsSync()) {
          file.deleteSync();
        }
        _recordingPath = null;
      }

      _isRecording = false;
      _recordingTimer?.cancel();
      _recordingTimer = null;
    } catch (e) {
      debugPrint('Error cleaning up recording: $e');
    }
  }

  /// Cleans up resources when the cubit is closed.
  @override
  Future<void> close() async {
    _recordingTimer?.cancel();
    await _audioRecorder.dispose();
    _cleanupRecording();
    return super.close();
  }
}
