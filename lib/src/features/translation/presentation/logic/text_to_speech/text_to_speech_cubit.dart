import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/repositories/text_to_speech_repository.dart';
import 'text_to_speech_state.dart';

class TextToSpeechCubit extends Cubit<TextToSpeechState> {
  final TextToSpeechRepository repository;

  TextToSpeechCubit({required this.repository}) : super(TextToSpeechState());

  Future<void> convertKurdishToSpeech({
    required String text,
    required bool isKurmanji,
  }) async {
    emit(state.copyWith(kurdishTTS: const AsyncValue.loading()));

    final result = await repository.convertKurdishToSpeech(text: text, isKurmanji: isKurmanji);

    result.fold(
      (error) => emit(state.copyWith(kurdishTTS: AsyncValue.error(error))),
      (data) => emit(state.copyWith(kurdishTTS: AsyncValue.success(data))),
    );
  }

  Future<void> convertOtherLanguageToSpeech({
    required String text,
    required String languageCode,
  }) async {
    emit(state.copyWith(otherLanguageTTS: const AsyncValue.loading()));

    final result = await repository.convertOtherLanguageToSpeech(
      text: text,
      languageCode: languageCode,
    );

    result.fold(
      (error) => emit(state.copyWith(otherLanguageTTS: AsyncValue.error(error))),
      (data) => emit(state.copyWith(otherLanguageTTS: AsyncValue.success(data))),
    );
  }
}
