import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/common/widgets/button_view.dart';
import '../../../../core/common/widgets/card_view.dart';
import '../../../../core/common/widgets/loading_view.dart';
import '../../../../core/common/widgets/snackbar/snackbar_view.dart';
import '../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../../../injection.dart';
import '../widgets/language_selector.dart';
import '../logic/translation/translation_cubit.dart';
import '../logic/translation/translation_state.dart';
import '../widgets/tts_button.dart';
import '../widgets/stt_button.dart';
import '../logic/speech_to_text/speech_to_text_cubit.dart';

import 'dart:ui' as ui;

class TextPage extends StatefulWidget {
  const TextPage({super.key});

  @override
  State<TextPage> createState() => _TextPageState();
}

class _TextPageState extends State<TextPage> {
  String sourceLanguage = 'English';
  String targetLanguage = 'Kurdish (Sorani)';
  final List<String> languages = ['English', 'Kurdish (Sorani)', 'Kurdish (Kurmanji)', 'Arabic'];

  // Language code mapping
  final Map<String, String> languageCodes = {'English': 'en', 'Kurdish (Sorani)': 'ku', 'Kurdish (Kurmanji)': 'kmr', 'Arabic': 'ar'};

  // Add state variables for translation results
  String? translationResult;
  String? googleTranslationResult;
  bool isTranslating = false;

  TextEditingController textFieldController = TextEditingController();

  /// Swaps the source and target languages
  ///
  /// @return void
  ///
  /// Example:
  /// Previous: Source=ES, Target=EN
  /// User selects ES as target → becomes Source=EN, Target=ES
  void _swapLanguages() {
    setState(() {
      final temp = sourceLanguage;
      sourceLanguage = targetLanguage;
      targetLanguage = temp;
      textFieldController.clear();
    });
  }

  /// Handles target language changes
  ///
  /// @param newValue: The newly selected target language
  ///
  /// If new target matches current source:
  /// 1. Stores previous target value
  /// 2. Swaps languages by setting:
  ///    - Source to previous target value
  ///    - Target to new value
  ///
  /// Example:
  /// Previous: Source=ES, Target=EN
  /// User selects ES as target → becomes Source=EN, Target=ES
  void _targetLanguageChanged(newValue) {
    if (newValue == null) return;
    textFieldController.clear();

    if (newValue == sourceLanguage) {
      final oldTarget = targetLanguage;
      setState(() {
        sourceLanguage = oldTarget;
        targetLanguage = newValue;
      });
    } else {
      setState(() {
        targetLanguage = newValue;
      });
    }
  }

  /// Handles target language changes
  ///
  /// @param newValue: The newly selected target language
  ///
  /// If new target matches current source:
  /// 1. Stores previous target value
  /// 2. Swaps languages by setting:
  ///    - Source to previous target value
  ///    - Target to new value
  ///
  /// Example:
  /// Previous: Source=ES, Target=EN
  /// User selects ES as target → becomes Source=EN, Target=ES
  void _sourceLanguageChanged(newValue) {
    if (newValue == null) return;
    textFieldController.clear();

    if (newValue == targetLanguage) {
      final oldSource = sourceLanguage;
      setState(() {
        targetLanguage = oldSource;
        sourceLanguage = newValue;
      });
    } else {
      setState(() {
        sourceLanguage = newValue;
      });
    }
  }

  // Add translation function using our cubit
  void _translateText() {
    if (textFieldController.text.isEmpty) return;

    final String srcLangCode = languageCodes[sourceLanguage] ?? 'en';
    final String targetLangCode = languageCodes[targetLanguage] ?? 'ku';

    context.read<TranslationCubit>().translate(
          srcLang: srcLangCode,
          targetLang: targetLangCode,
          content: textFieldController.text,
        );
  }

  @override
  void dispose() {
    textFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => serviceLocator<SpeechToTextCubit>(),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    TextView(
                      text: 'text_page_title',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    TextView(
                      text: 'text_page_description',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 56),
                    LanguageSelector(
                      sourceLanguage: sourceLanguage,
                      targetLanguage: targetLanguage,
                      languages: languages,
                      onSourceLanguageChanged: _sourceLanguageChanged,
                      onTargetLanguageChanged: _targetLanguageChanged,
                      onSwap: _swapLanguages,
                    ),
                    const SizedBox(height: 16),
                    CardView(
                      borderRadius: 16,
                      elevation: 1,
                      color: Colors.white,
                      child: Column(
                        children: [
                          // change this textFormFieldView if possible
                          TextField(
                            onSubmitted: (value) {
                              _translateText();
                            },
                            maxLines: 8,
                            controller: textFieldController,
                            onTapOutside: (value) {
                              FocusScope.of(context).unfocus();
                            },
                            onChanged: (value) {
                              setState(() {});
                            },
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  fontFamily: 'Titillium',
                                  fontSize: 18,
                                ),
                            decoration: InputDecoration(
                              disabledBorder: InputBorder.none,
                              errorBorder: InputBorder.none,
                              focusedErrorBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              border: InputBorder.none,
                              hintText: "Write_here".tr(),
                              hintStyle: TextStyle(
                                fontFamily: targetLanguage == 'English' ? 'Titillium' : 'Rabar015',
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              if (textFieldController.text.isNotEmpty)
                                TTSButtonView(
                                  text: textFieldController.text,
                                  languageCode: languageCodes[sourceLanguage] ?? 'en',
                                  isKurdish: sourceLanguage.contains('Kurdish'),
                                ),
                              STTButtonView(
                                languageCode: languageCodes[sourceLanguage] ?? 'en',
                                isKurdish: sourceLanguage.contains('Kurdish'),
                                onTextExtracted: (text) {
                                  setState(() {
                                    textFieldController.text = textFieldController.text.isEmpty ? text : '${textFieldController.text} $text';
                                    textFieldController.selection = TextSelection.fromPosition(
                                      TextPosition(offset: textFieldController.text.length),
                                    );
                                  });
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          BlocConsumer<TranslationCubit, TranslationState>(
                            listener: (context, state) {
                              state.translateState.maybeWhen(
                                orElse: () {},
                                success: (translation) {
                                  FocusScope.of(context).unfocus();
                                  setState(() {
                                    translationResult = translation.translation;
                                    googleTranslationResult = translation.googleTranslation;
                                  });
                                },
                                error: (error) {
                                  FocusScope.of(context).unfocus();
                                  SnackbarView.show(
                                    context,
                                    message: error.error?.message ?? "text_page_translation_failed",
                                    onAction: () {},
                                  );
                                },
                              );
                            },
                            builder: (context, state) {
                              return state.translateState.maybeWhen(
                                orElse: () => ButtonView(
                                  buttonType: ButtonType.solidButton,
                                  onClick: _translateText,
                                  isDisabled: textFieldController.text.isEmpty,
                                  semanticLabelValue: 'Translate button',
                                  title: 'text_page_translate_button',
                                ),
                                loading: () => const LoadingView(),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),
                    // BLA Translation Result Card
                    if (translationResult != null)
                      CardView(
                        borderRadius: 16,
                        elevation: 1,
                        color: Colors.white,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Image.asset(
                                    'assets/images/icons/microsoft_translate.png',
                                    height: 24,
                                    width: 24,
                                  ),
                                  const SizedBox(width: 8),
                                  TextView(
                                    text: 'text_page_microsoft_translate',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const Spacer(),
                                  TTSButtonView(
                                    text: translationResult!,
                                    languageCode: languageCodes[targetLanguage] ?? 'ku',
                                    isKurdish: targetLanguage.contains('Kurdish'),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: Text(
                                  translationResult ?? '',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                        fontFamily: targetLanguage == 'English' ? 'Titillium' : 'Rabar015',
                                        fontSize: 18,
                                      ),
                                  textAlign: targetLanguage == 'English' ? TextAlign.left : TextAlign.right,
                                  textDirection: targetLanguage == 'English' ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    // Google Translate Result Card
                    if (googleTranslationResult != null)
                      CardView(
                        borderRadius: 16,
                        elevation: 1,
                        color: Colors.white,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Image.asset(
                                    'assets/images/icons/google_translate.png',
                                    height: 24,
                                    width: 24,
                                    errorBuilder: (context, error, stackTrace) => Icon(
                                      Icons.g_translate,
                                      size: 24,
                                      color: Colors.blue,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  TextView(
                                    text: 'text_page_google_translate',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const Spacer(),
                                  TTSButtonView(
                                    text: googleTranslationResult!,
                                    languageCode: languageCodes[targetLanguage] ?? 'ku',
                                    isKurdish: targetLanguage.contains('Kurdish'),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: Text(
                                  googleTranslationResult ?? '',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                        fontFamily: targetLanguage == 'English' ? 'Titillium' : 'Rabar015',
                                        fontSize: 18,
                                      ),
                                  textAlign: targetLanguage == 'English' ? TextAlign.left : TextAlign.right,
                                  textDirection: targetLanguage == 'English' ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
