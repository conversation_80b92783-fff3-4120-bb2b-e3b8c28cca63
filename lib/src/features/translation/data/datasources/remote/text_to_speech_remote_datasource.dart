import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/http/http_methods.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/payloads/text_to_speech_payload.dart';

abstract class TextToSpeechRemoteDataSource {
  Future<Map<String, dynamic>> convertKurdishToSpeech(KurdishTTSPayload payload);
  Future<Map<String, dynamic>> convertOtherLanguageToSpeech(OtherLanguageTTSPayload payload);
}

class TextToSpeechRemoteDataSourceImpl implements TextToSpeechRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  TextToSpeechRemoteDataSourceImpl({required this.httpManager, required this.aesEncryptionManager});

  @override
  Future<Map<String, dynamic>> convertKurdishToSpeech(KurdishTTSPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(payload.toJson());
    final Response response = await httpManager.request(
      path: Api().textToSpeechKurdish,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(decodedData['data']);

    decodedData = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }

  @override
  Future<Map<String, dynamic>> convertOtherLanguageToSpeech(OtherLanguageTTSPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(payload.toJson());

    final Response response = await httpManager.request(
      path: Api().textToSpeechOther,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(decodedData['data']);

    decodedData = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }
}
