import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../core/utils/managers/http/http_methods.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/payloads/translation_payload.dart';

abstract class TranslationRemoteDataSource {
  Future<Map<String, dynamic>> translate(TranslationPayload payload);
}

class TranslationRemoteDataSourceImpl implements TranslationRemoteDataSource {
  final HttpManager httpManager;
  final AESEncryptionManager aesEncryptionManager;

  TranslationRemoteDataSourceImpl({required this.httpManager, required this.aesEncryptionManager});

  @override
  Future<Map<String, dynamic>> translate(TranslationPayload payload) async {
    final String encryptedPayload = await aesEncryptionManager.encryptedPayload(
      payload.toJson(),
      keyType: AESKeyType.translation,
    );

    final Response response = await httpManager.request(
      path: Api().translate,
      payload: {"data": encryptedPayload},
      method: HttpMethods.post,
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
      },
    );

    Map<String, dynamic> decodedData = json.decode(response.data as String) as Map<String, dynamic>;

    final String decryptedData = await aesEncryptionManager.decryptData(
      decodedData['data'],
      keyType: AESKeyType.translation,
    );

    decodedData['data'] = json.decode(decryptedData) as Map<String, dynamic>;

    return decodedData;
  }
}
