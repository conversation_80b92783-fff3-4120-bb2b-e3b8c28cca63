import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';

import '../../../../../core/api/api.dart';
import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/utils/helpers/error_parser.dart';
import '../../../../../core/utils/managers/security/aes_encryption_manager.dart';
import '../../models/responses/speech_to_text_response_model.dart';

abstract class SpeechToTextRemoteDataSource {
  Future<SpeechToTextResponseModel> convertKurdishSpeechToText({
    required String audioFilePath,
    required bool isKurmanji,
  });

  Future<SpeechToTextResponseModel> convertOtherLanguageSpeechToText({
    required String audioFilePath,
    required String languageCode,
  });
}

class SpeechToTextRemoteDataSourceImpl implements SpeechToTextRemoteDataSource {
  final AESEncryptionManager aesEncryptionManager;

  SpeechToTextRemoteDataSourceImpl({required this.aesEncryptionManager});

  @override
  Future<SpeechToTextResponseModel> convertKurdishSpeechToText({
    required String audioFilePath,
    required bool isKurmanji,
  }) async {
    try {
      // Create a file object to make sure it exists
      final File file = File(audioFilePath);
      if (!await file.exists()) {
        throw ErrorModel(
          error: Error(message: 'File not found: $audioFilePath'),
        );
      }

      // Get file name from path
      final String fileName = audioFilePath.split('/').last;

      // Create Dio with appropriate timeouts
      final dio = Dio();
      dio.options.connectTimeout = const Duration(minutes: 2);
      dio.options.receiveTimeout = const Duration(minutes: 5);
      dio.options.sendTimeout = const Duration(minutes: 2);

      // Create multipart file
      final MultipartFile multipartFile = await MultipartFile.fromFile(
        audioFilePath,
        filename: fileName,
      );

      // Get API credentials

      var data = {'text_to_speechKrmanji': isKurmanji ? 'true' : 'false'};

      var encryptedData = await aesEncryptionManager.encryptedPayload(
        data,
        keyType: AESKeyType.translation,
      );

      // Create FormData with the required fields

      final formData = FormData.fromMap({
        'file': multipartFile,
        'data': encryptedData,
      });
      // FormData is ready for sending

      // Make the API request with FormData
      final response = await dio.post(
        Api().speechToTextKurdish,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => true,
        ),
      );

      // removedLog log(response.toString());

      // Process response

      // Handle response
      if (response.statusCode == 200) {
        final String decryptedData = await aesEncryptionManager.decryptData(
          response.data["data"],
          keyType: AESKeyType.translation,
        );

        Map<String, dynamic> decodedData = json.decode(decryptedData) as Map<String, dynamic>;

        // Extract text from the response
        if (decodedData.containsKey('text')) {
          return SpeechToTextResponseModel(
            text: decodedData['text'].toString(),
            status: decodedData['status']?.toString(),
          );
        } else {
          return SpeechToTextResponseModel(text: '');
        }
      } else {
        String errorMessage = 'Server error';

        if (response.data is Map) {
          errorMessage = response.data['message'] ?? 'Unknown server error';
          if (response.data['errors'] != null) {}
        } else if (response.data is String) {
          errorMessage = response.data;
        }
        throw ErrorModel(
          error: Error(message: 'Error ${response.statusCode}: $errorMessage'),
        );
      }
    } catch (e) {
      if (e is ErrorModel) {
        rethrow;
      }

      if (e is DioException && e.type == DioExceptionType.receiveTimeout) {
        throw ErrorModel(
          error: Error(
            message: 'The speech recognition is taking longer than expected. Please try a shorter audio clip or try again later.',
          ),
        );
      }

      throw errorParser(e, StackTrace.current);
    }
  }

  @override
  Future<SpeechToTextResponseModel> convertOtherLanguageSpeechToText({
    required String audioFilePath,
    required String languageCode,
  }) async {
    try {
      // Create a file object to make sure it exists
      final File file = File(audioFilePath);
      if (!await file.exists()) {
        throw ErrorModel(error: Error(message: 'File not found: $audioFilePath'));
      }

      // Get file name from path
      final String fileName = audioFilePath.split('/').last;

      // Create Dio with appropriate timeouts
      final dio = Dio();
      dio.options.connectTimeout = const Duration(minutes: 2);
      dio.options.receiveTimeout = const Duration(minutes: 5);
      dio.options.sendTimeout = const Duration(minutes: 2);

      // Create multipart file
      final MultipartFile multipartFile = await MultipartFile.fromFile(
        audioFilePath,
        filename: fileName,
      );

      // Get API credentials

      log(languageCode);

      var data = {
        'lang': languageCode,
      };

      var encryptedData = await aesEncryptionManager.encryptedPayload(
        data,
        keyType: AESKeyType.translation,
      );

      final formData = FormData.fromMap({
        'file': multipartFile,
        'data': encryptedData,
      });

      final response = await dio.post(
        Api().speechToTextOther,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => true,
        ),
      );

      inspect(response.data);

      // Handle response
      if (response.statusCode == 200) {
        // removedLog log(response.data);

        final String decryptedData = await aesEncryptionManager.decryptData(
          response.data["data"],
          keyType: AESKeyType.translation,
        );

        Map<String, dynamic> decodedData = json.decode(decryptedData) as Map<String, dynamic>;

        if (decodedData.containsKey('text')) {
          return SpeechToTextResponseModel(
            text: decodedData['text'].toString(),
            status: decodedData['status']?.toString(),
          );
        } else {
          return SpeechToTextResponseModel(text: '');
        }
      } else {
        String errorMessage = 'Server error';

        if (response.data is Map) {
          errorMessage = response.data['message'] ?? 'Unknown server error';
          // Check if there are validation errors
          if (response.data['errors'] != null) {}
        } else if (response.data is String) {
          errorMessage = response.data;
        }

        throw ErrorModel(
          error: Error(message: 'Error ${response.statusCode}: $errorMessage'),
        );
      }
    } catch (e) {
      if (e is ErrorModel) {
        rethrow;
      }

      if (e is DioException && e.type == DioExceptionType.receiveTimeout) {
        throw ErrorModel(
          error: Error(
            message: 'The speech recognition is taking longer than expected. Please try a shorter audio clip or try again later.',
          ),
        );
      }

      throw errorParser(e, StackTrace.current);
    }
  }
}
