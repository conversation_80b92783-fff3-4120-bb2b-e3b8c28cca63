import 'dart:developer';

import 'package:dartz/dartz.dart';

import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/utils/helpers/error_parser.dart';
import '../../domain/entities/text_to_speech_entity.dart';
import '../../domain/repositories/text_to_speech_repository.dart';
import '../datasources/remote/text_to_speech_remote_datasource.dart';
import '../models/payloads/text_to_speech_payload.dart';
import '../models/text_to_speech_response_model.dart';

class TextToSpeechRepositoryImpl implements TextToSpeechRepository {
  final TextToSpeechRemoteDataSource textToSpeechRemoteDataSource;

  TextToSpeechRepositoryImpl({required this.textToSpeechRemoteDataSource});

  @override
  Future<Either<ErrorModel, TextToSpeechEntity>> convertKurdishToSpeech({required String text, required bool isKurmanji}) async {
    try {
      final payload = KurdishTTSPayload(sentences: text, isKurmanji: isKurmanji);

      final response = await textToSpeechRemoteDataSource.convertKurdishToSpeech(payload);

      final responseModel = KurdishTTSResponseModel.fromJson(response);

      // change it to entity
      TextToSpeechEntity textToSpeechEntity = TextToSpeechEntity(audioFileUrl: responseModel.fileUrl);

      return Right(textToSpeechEntity);
    } catch (error, stackTrace) {
      log("repo error: $error");
      return Left(errorParser(error, stackTrace));
    }
  }

  @override
  Future<Either<ErrorModel, TextToSpeechEntity>> convertOtherLanguageToSpeech({required String text, required String languageCode}) async {
    try {
      final payload = OtherLanguageTTSPayload(
        sentences: text,
        languageCode: languageCode,
      );

      final response = await textToSpeechRemoteDataSource.convertOtherLanguageToSpeech(payload);
      final responseModel = OtherLanguageTTSResponseModel.fromJson(response);

      return Right(
        TextToSpeechEntity(
          audioFileUrl: responseModel.fileUrl,
          status: responseModel.status,
        ),
      );
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
