import 'package:dartz/dartz.dart';

import '../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../core/utils/helpers/error_parser.dart';
import '../../domain/entities/translation_entity.dart';
import '../../domain/repositories/translation_repository.dart';
import '../datasources/remote/translation_remote_datasource.dart';
import '../mappers/translation_mapper.dart';
import '../models/payloads/translation_payload.dart';
import '../models/translation_response_model.dart';

class TranslationRepositoryImpl implements TranslationRepository {
  final TranslationRemoteDataSource translationRemoteDataSource;

  TranslationRepositoryImpl({required this.translationRemoteDataSource});

  @override
  Future<Either<ErrorModel, TranslationEntity>> translate({required String srcLang, required String targetLang, required String content}) async {
    try {
      final TranslationPayload payload = TranslationPayload(srcLang: srcLang, targetLang: targetLang, content: content);

      final responseData = await translationRemoteDataSource.translate(payload);

      final Map<String, dynamic> translationData = responseData['data'] as Map<String, dynamic>;

      final translationResponse = TranslationResponseModel.fromJson(translationData);

      final TranslationMapper translationMapper = TranslationMapper(translationModel: translationResponse);

      final TranslationEntity translationEntity = translationMapper.toTranslationEntity();

      return Right(translationEntity);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
