import 'package:flutter/material.dart';

import 'app_space_widget.dart';
import 'button_view.dart';
import 'text_widgets/text_view.dart';

class ErrorView extends StatelessWidget {
  final String? error;
  final VoidCallback? onRefresh;
  final bool showLogo;
  final double? logoSize;

  const ErrorView({
    this.showLogo = true,
    this.error,
    this.onRefresh,
    this.logoSize,
    super.key,
  });

  factory ErrorView.somethingWentWrong({required VoidCallback onRefresh}) {
    return SomethingWentWrong(
      onRefresh: onRefresh,
    );
  }

  factory ErrorView.custom(
      {required VoidCallback onRefresh, String? title, String? subTitle}) {
    return CustomError(
      onRefresh: onRefresh,
      title: title,
      subTitle: subTitle,
    );
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }
}

class SomethingWentWrong extends ErrorView {
  const SomethingWentWrong({super.key, required super.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AspectRatio(
        aspectRatio: 1,
        child: Column(
          children: [
            TextView(
              text: "something_went_wrong",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            AppSpacer.p16(),
            TextView(
              text: "something_went_wrong_please_try_again_later",
              style: Theme.of(context).textTheme.bodySmall,
            ),
            AppSpacer.p32(),
            SizedBox(
              width: 200,
              child: ButtonView(
                buttonType: ButtonType.solidButton,
                semanticLabelValue: "refresh",
                onClick: onRefresh ?? () {},
                title: "refresh",
                isExpanded: false,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomError extends ErrorView {
  final String? title;
  final String? subTitle;

  const CustomError({
    super.key,
    required super.onRefresh,
    this.title,
    this.subTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AspectRatio(
        aspectRatio: 1,
        child: Column(
          children: [
            TextView(
              text: title ?? "something_went_wrong",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            AppSpacer.p16(),
            TextView(
              text: subTitle ?? "something_went_wrong_please_try_again_later",
              style: Theme.of(context).textTheme.bodySmall,
            ),
            AppSpacer.p32(),
            SizedBox(
              width: 200,
              child: ButtonView(
                buttonType: ButtonType.solidButton,
                semanticLabelValue: "Refresh",
                onClick: onRefresh ?? () {},
                title: "refresh",
                isExpanded: false,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NoDataFoundPage extends StatelessWidget {
  const NoDataFoundPage({super.key, this.title, this.subtitle});

  final String? title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AspectRatio(
        aspectRatio: 1,
        child: Column(
          children: [
            if (title != null)
              TextView(
                text: title!,
                style: Theme.of(context).textTheme.titleMedium,
              ),
            if (subtitle != null) ...[
              AppSpacer.p16(),
              TextView(
                text: subtitle!,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
