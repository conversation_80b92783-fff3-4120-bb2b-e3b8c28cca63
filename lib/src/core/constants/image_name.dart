enum ImageName {
  logo,
  dinar,
  trophy,
  email,
  lock,
  google,
  apple,
  signupOnboarding,
  owl,
  badge,
  levels,
  somethingWentWrong,
  timeIsUp,
  heartLives,
  brokenHeart,
  leftArcShape,
  rightArcShape,
  rightArcFlipped,
  leftArcFlipped,
  horizontalRoad,
  verticalRoad,
  emptyBadge,
  noChildAccountYet,
  testFailed,
  signupChild,
  description,
  settingsIcon,
  editIcon,
  person,
  owlAnimation,
  owlFailedAnimation,
}

class ImageNameHelper {
  static String getValue(ImageName path) {
    switch (path) {
      case ImageName.logo:
        return "assets/logo/logo.png";
      default:
        return "assets/images/logo.png";
    }
  }
}
