import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

const kKu = 'ar';
const kAr = 'ar';
const kEn = 'en';

const kEG = 'EG';
const kIQ = 'IQ';
const kUS = 'US';

const String kPhoneNumber = '000000000';

const String kAndroidURL = "";
const String kIosURL = "";
const String kHuaweiURL = "";

const double kButtonHeight = 40.0;
const BorderRadius borderRadius = BorderRadius.all(Radius.circular(8));

Map<String, String> genderOptions = {
  'M': 'Male',
  'F': 'Female',
  'O': 'Other',
};

// Secure API credentials from .env file
String? getTranslationAseKey() => dotenv.env['TRANSLATION_AES_KEY'];
String? getAuthAesKey() => dotenv.env['AUTH_AES_KEY'];
String? getAesIv() => dotenv.env['AES_IV'];
