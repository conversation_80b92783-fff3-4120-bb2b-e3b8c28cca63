import '../managers/security/aes_encryption_manager.dart';

/// Helper class to provide convenient methods for encryption/decryption
/// with the appropriate key types for different route categories
class EncryptionHelper {
  final AESEncryptionManager _aesEncryptionManager;

  EncryptionHelper(this._aesEncryptionManager);

  // ========== AUTH ROUTES METHODS ==========
  
  /// Encrypts payload for authentication routes (login, signup, forgot password, etc.)
  /// Uses the AUTH_AES_KEY from environment variables
  Future<String> encryptAuthPayload(Map<String, dynamic>? payload) async {
    return await _aesEncryptionManager.encryptedPayload(
      payload,
      keyType: AESKeyType.auth,
    );
  }

  /// Decrypts data for authentication routes (login, signup, forgot password, etc.)
  /// Uses the AUTH_AES_KEY from environment variables
  Future<String> decryptAuthData(String encryptedData) async {
    return await _aesEncryptionManager.decryptData(
      encryptedData,
      keyType: AESKeyType.auth,
    );
  }

  // ========== TRANSLATION/MAIN APP ROUTES METHODS ==========
  
  /// Encrypts payload for main app features (translation, dictionary, speech-to-text, etc.)
  /// Uses the TRANSLATION_AES_KEY from environment variables
  Future<String> encryptTranslationPayload(Map<String, dynamic>? payload) async {
    return await _aesEncryptionManager.encryptedPayload(
      payload,
      keyType: AESKeyType.translation,
    );
  }

  /// Decrypts data for main app features (translation, dictionary, speech-to-text, etc.)
  /// Uses the TRANSLATION_AES_KEY from environment variables
  Future<String> decryptTranslationData(String encryptedData) async {
    return await _aesEncryptionManager.decryptData(
      encryptedData,
      keyType: AESKeyType.translation,
    );
  }

  // ========== GENERIC METHODS (for backward compatibility) ==========
  
  /// Generic encryption method that allows specifying the key type
  Future<String> encryptPayload(
    Map<String, dynamic>? payload, {
    AESKeyType keyType = AESKeyType.auth,
  }) async {
    return await _aesEncryptionManager.encryptedPayload(payload, keyType: keyType);
  }

  /// Generic decryption method that allows specifying the key type
  Future<String> decryptData(
    String encryptedData, {
    AESKeyType keyType = AESKeyType.auth,
  }) async {
    return await _aesEncryptionManager.decryptData(encryptedData, keyType: keyType);
  }
}
