import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../../app/logic/app_settings.dart';
import '../../../../injection.dart';
import '../../../api/api.dart';
import '../../../enums/secure_storage_key.dart';
import '../../helpers/colored_dio_logger.dart';
import '../database/database_manager.dart';
import 'http_methods.dart';

/// HTTP Manager for handling API requests and responses
///
/// This file contains the implementation of HTTP client functionality using Dio.
/// It handles authentication, token refresh, request/response logging, and error handling.
///
/// Abstract class defining the contract for HTTP operations
/// This interface defines the methods that any HTTP manager implementation must provide.
abstract class HttpManager {
  /// Makes an HTTP request to the specified endpoint
  ///
  /// Parameters:
  /// - [path]: The API endpoint path
  /// - [method]: HTTP method (GET, POST, PUT, DELETE)
  /// - [params]: Optional query parameters
  /// - [headers]: Optional HTTP headers
  /// - [payload]: Optional request body data
  /// - [customBaseUrl]: Optional custom base URL (overrides default)
  /// - [timeOut]: Optional custom timeout duration
  Future<Response> request({
    required String path,
    required HttpMethods method,
    Map<String, dynamic>? params,
    Map<String, dynamic>? headers,
    Object? payload,
    String? customBaseUrl,
    Duration? timeOut,
  });

  /// Enables performance logging for HTTP requests
  void runPerformanceLog();

  /// Enables or disables HTTP request/response logging
  ///
  /// Parameters:
  /// - [enabled]: Whether logging should be enabled or disabled
  void setLoggingEnabled(bool enabled);
}

/// Implementation of the HttpManager interface
///
/// Handles all HTTP communication in the app with support for:
/// - Token-based authentication
/// - Automatic token refresh
/// - Request/response logging
/// - Error handling
class HttpManagerImpl implements HttpManager {
  /// Main Dio instance for regular API requests
  late final Dio _dio;

  /// Separate Dio instance for authentication-related requests
  late final Dio _authDio;

  /// Database manager for secure storage operations (tokens)
  late final DatabaseManager _databaseManager;

  // final FirebasePerformanceManager _dioFirebasePerformanceInterceptor =
  //     FirebasePerformanceManager();

  /// Flag to track if performance logging is enabled
  bool isPerformanceLogActive = false;

  /// Flag to track if logging is enabled
  bool isLoggingEnabled = false;

  /// Creates a new HttpManagerImpl instance
  ///
  /// Parameters:
  /// - [baseOptions]: Dio configuration options
  /// - [databaseManager]: Database manager for token storage
  HttpManagerImpl({
    required BaseOptions baseOptions,
    required DatabaseManager databaseManager,
  }) {
    initHttpManager(
      baseOptions: baseOptions,
      databaseManager: databaseManager,
    );
  }

  /// Initializes the HTTP manager with Dio instances and interceptors
  ///
  /// Sets up two Dio instances:
  /// - _dio: For regular API requests with token authentication
  /// - _authDio: For authentication-specific requests
  Future<void> initHttpManager({required BaseOptions baseOptions, required DatabaseManager databaseManager}) async {
    _dio = Dio(baseOptions);

    _authDio = Dio(baseOptions);
    _databaseManager = databaseManager;

    _authDio.interceptors.addAll([_appAuthInterceptor()]);

    _dio.interceptors.addAll([_appInterceptor()]);

    if (isLoggingEnabled) {
      // Create a custom colored logger
      ColoredDioLogger coloredLogger = ColoredDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 120,
        enabled: kDebugMode,
      );

      // Add the logger to both Dio instances
      _dio.interceptors.add(coloredLogger);
      _authDio.interceptors.add(coloredLogger);
    }
  }

  /// Enables or disables HTTP request/response logging
  ///
  /// Use this method to toggle logging on or off at runtime
  @override
  void setLoggingEnabled(bool enabled) {
    isLoggingEnabled = enabled;

    if (!enabled) {
      // Remove any ColoredDioLogger instances from both Dio instances
      _dio.interceptors.removeWhere((interceptor) => interceptor is ColoredDioLogger);
      _authDio.interceptors.removeWhere((interceptor) => interceptor is ColoredDioLogger);
    } else {
      // Re-add loggers if they're not already present
      if (!_dio.interceptors.any((i) => i is ColoredDioLogger)) {
        _dio.interceptors.add(ColoredDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 120,
          enabled: kDebugMode,
        ));
      }

      if (!_authDio.interceptors.any((i) => i is ColoredDioLogger)) {
        _authDio.interceptors.add(ColoredDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 120,
          enabled: kDebugMode,
        ));
      }
    }
  }

  /// Enables performance logging for HTTP requests
  ///
  /// Currently has Firebase performance monitoring code commented out.
  /// Only sets the flag to true when called.
  @override
  void runPerformanceLog() {
    try {
      if (!isPerformanceLogActive) {
        // Firebase performance monitoring (commented out)
        // _authDio.interceptors.add(_dioFirebasePerformanceInterceptor);
        // _dio.interceptors.add(_dioFirebasePerformanceInterceptor);

        // Make sure colored logger is added if logging is enabled
        if (isLoggingEnabled) {
          if (!_dio.interceptors.any((i) => i is ColoredDioLogger)) {
            _dio.interceptors.add(ColoredDioLogger(
              requestHeader: true,
              requestBody: true,
              responseBody: true,
              responseHeader: false,
              error: true,
              compact: true,
              maxWidth: 120,
              enabled: kDebugMode,
            ));
          }

          if (!_authDio.interceptors.any((i) => i is ColoredDioLogger)) {
            _authDio.interceptors.add(ColoredDioLogger(
              requestHeader: true,
              requestBody: true,
              responseBody: true,
              responseHeader: false,
              error: true,
              compact: true,
              maxWidth: 120,
              enabled: kDebugMode,
            ));
          }
        }

        isPerformanceLogActive = true;
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  /// Makes an HTTP request to the specified endpoint
  ///
  /// Core method for making all types of HTTP requests (GET, POST, PUT, DELETE).
  /// Configures the request with appropriate timeout, headers, parameters, and base URL.
  ///
  /// Parameters:
  /// - [path]: The API endpoint path
  /// - [method]: HTTP method (GET, POST, PUT, DELETE)
  /// - [params]: Optional query parameters
  /// - [headers]: Optional HTTP headers
  /// - [payload]: Optional request body data
  /// - [customBaseUrl]: Optional custom base URL (overrides default)
  /// - [timeOut]: Optional custom timeout duration
  @override
  Future<Response> request(
      {required String path, required HttpMethods method, Map<String, dynamic>? params, Map<String, dynamic>? headers, Object? payload, String? customBaseUrl, Duration? timeOut}) async {
    if (timeOut != null) {
      _dio.options.connectTimeout = timeOut;
      _dio.options.receiveTimeout = timeOut;
      _dio.options.sendTimeout = timeOut;
    } else {
      _dio.options.connectTimeout = const Duration(seconds: 15);
      _dio.options.receiveTimeout = const Duration(seconds: 15);
      _dio.options.sendTimeout = const Duration(seconds: 15);
    }

    // Add custom headers if provided
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }

    // Configure query parameters
    if (params != null) {
      _dio.options.queryParameters.addAll(params);
    } else {
      _dio.options.queryParameters.clear();
    }

    // Set base URL (custom or from app settings)
    if (customBaseUrl != null) {
      _dio.options.baseUrl = customBaseUrl;
    } else {
      _dio.options.baseUrl = serviceLocator<AppSettings>().apiBaseUrl ?? '';
    }

    // Execute the appropriate HTTP method
    switch (method) {
      case HttpMethods.get:
        return _dio.get(path);
      case HttpMethods.post:
        return _dio.post(
          path,
          data: payload,
        );
      case HttpMethods.put:
        return _dio.put(path, data: payload);
      case HttpMethods.delete:
        return _dio.delete(path);
    }
  }
}

/// Extension methods for HttpManagerImpl to handle interceptors and token refresh
extension HttpManagerImplHelpers on HttpManagerImpl {
  /// Creates an interceptor for authentication-related requests
  ///
  /// This interceptor is simpler than the main interceptor and doesn't handle token refresh.
  /// It's used by the _authDio instance for authentication-specific requests.
  InterceptorsWrapper _appAuthInterceptor() {
    return InterceptorsWrapper(
      // Configure request before sending
      onRequest: (options, handler) {
        options.baseUrl = serviceLocator<AppSettings>().apiBaseUrl ?? "";

        // debugPrint(
        //   'Call: => BASE: ${options.baseUrl}',
        // );

        return handler.next(options);
      },
      // Log successful responses
      onResponse: (response, handler) {
        debugPrint(
          'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
        );
        return handler.next(response);
      },
      // Log and handle errors
      onError: (DioException err, handler) async {
        debugPrint(
          'ERROR[${err.response?.statusCode ?? 0}] => PATH: ${err.requestOptions.path}',
        );
        return handler.next(err);
      },
    );
  }

  /// Creates the main interceptor for regular API requests
  ///
  /// This interceptor:
  /// 1. Adds authentication token to request headers
  /// 2. Logs request/response details
  /// 3. Handles 401 (Unauthorized) errors by attempting to refresh the token
  InterceptorsWrapper _appInterceptor() {
    return InterceptorsWrapper(
      // Configure request before sending
      onRequest: (options, handler) async {
        // Set base URL if not already set
        if (options.baseUrl.isEmpty) {
          options.baseUrl = serviceLocator<AppSettings>().apiBaseUrl ?? "";
        }

        debugPrint(
          'Call: => BASE: ${options.baseUrl}',
        );

        // Get authentication token from secure storage
        final String? token = await _databaseManager.getSecureData(SecureStorageKey.token);

        // Add authentication token and other headers
        options.headers.addAll({'Authorization': 'Bearer $token'});

        log("PATH ${options.path}");
        log("HEADERS: ${options.headers}");
        log("DATA: ${options.data}");

        return handler.next(options);
      },
      // Log successful responses
      onResponse: (response, handler) {
        log(
          'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
        );

        log(
          ' PATH: ${response.requestOptions.path} response  : [RESPONSE]=> ${response.data}',
        );
        return handler.next(response);
      },
      // Handle errors, with special handling for 401 (Unauthorized)
      onError: (DioException err, handler) async {
        log(
          'ERROR[${err.response?.statusCode ?? 0}] => PATH: ${err.requestOptions.path}',
        );
        log(
          'ERROR response [${err.response ?? 0}] => PATH: ${err.requestOptions.path}',
        );

        // Handle 401 Unauthorized errors
        if ((err.response?.statusCode ?? 0) == 401) {
          // by default you must request to refresh token when we get 401
          // but for now we will just return the error
          // check the path if its login we need to return the error
          // if not we need to request to refresh token

          // If the error is from the login endpoint, just return the error
          if (err.requestOptions.path == Api().login) {
            return handler.next(err);
          } else {
            // For other endpoints, try to refresh the token
            // final RequestOptions? requestOptions = err.response?.requestOptions;
            // if (requestOptions != null) {
            //   final Either<dynamic, Response<dynamic>> response =
            //       await _refreshToken(requestOptions);

            // Handle the result of refresh token

            handler.next(err);
            // return response.fold(
            //   // Pass through the error if refresh token failed
            //   (loginError) => handler.next(loginError),
            //   // Return the new response if successful
            //   (recoveredResponse) => handler.resolve(recoveredResponse),
            // );
          }
        } else {
          // For non-401 errors, just pass through
          return handler.next(err);
        }
      },
    );
  }

  /// Attempts to refresh the authentication token
  ///
  /// When a request fails with a 401 error, this method:
  /// 1. Gets the refresh token from secure storage
  /// 2. Makes a request to get a new access token
  /// 3. Saves the new token
  /// 4. Recreates the original request with the new token
  ///
  /// Returns Either:
  /// - Left(error): If token refresh failed
  /// - Right(response): If token refresh succeeded and the original request was retried successfully
  ///
  ///
  // Future<Either<dynamic, Response<dynamic>>> _refreshToken(
  //     RequestOptions? requestOptions) async {
  //   try {
  //     // Get the refresh token from secure storage
  //     final oldRefreshToken =
  //         await _databaseManager.getSecureData(SecureStorageKey.refreshToken);

  //     // Request a new token using the refresh token
  //     final Response<dynamic> response = await _authDio.post(
  //       Api().newToken,
  //       data: {
  //         "refresh_token": oldRefreshToken,
  //       },
  //       options: Options(
  //         headers: {
  //           'Lang': 'en',
  //         },
  //       ),
  //     );

  //     // Parse the response to get the new token
  //     final responseData =
  //         json.decode(response.data as String) as Map<String, dynamic>;

  //     final LoginResponseModel responseModel =
  //         LoginResponseModel.fromJson(responseData);
  //     final String token = responseModel.token;

  //     // Save the new token to secure storage
  //     _databaseManager.saveSecureData(SecureStorageKey.token, token);

  //     // Update the original request with the new token
  //     requestOptions?.headers.addAll({'Authorization': 'Bearer $token'});

  //     // Recreate the original request with the new token
  //     final options = Options(
  //       method: requestOptions?.method,
  //       headers: requestOptions?.headers,
  //     );

  //     final Response cloneReq = await _dio.request(
  //       requestOptions?.path ?? '',
  //       data: requestOptions?.data,
  //       queryParameters: requestOptions?.queryParameters,
  //       options: options,
  //     );

  //     // Return the successful response
  //     return Right(cloneReq);
  //   } catch (error) {
  //     // Handle errors during token refresh
  //     if (error is DioException) {
  //       if (error.response?.statusCode == 401) {
  //         // If we get another 401, the refresh token is likely invalid too
  //         serviceLocator<AppRouter>().navigatorKey.currentContext;
  //         _databaseManager.deleteSecureData(SecureStorageKey.token);
  //         _databaseManager.deleteSecureData(SecureStorageKey.refreshToken);
  //       }
  //     }

  //     return Left(error);
  //   }
  // }
}
