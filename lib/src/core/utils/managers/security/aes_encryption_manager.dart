import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

import '../../../constants/const.dart';

abstract class AESEncryptionManager {
  /// Encrypts the provided [data] using AES encryption.
  ///
  /// This method takes a plain text string and returns an encrypted string.
  ///
  /// Example Input: 'Hello World'
  /// Example Output: 'U2FsdGVkX1+...'
  Future<String> encryptData(String data);

  /// Decrypts the provided [encryptedData] back to its original plain text.
  ///
  /// This method takes an encrypted string and returns the original plain text string.
  ///
  /// Example Input: 'U2FsdGVkX1+...'
  /// Example Output: 'Hello World'
  Future<String> decryptData(String encryptedData);

  /// Encrypts a payload represented as a [Map] of key-value pairs.
  ///
  /// This method takes a map, converts it to a JSON string, and then encrypts it.
  ///
  /// Example Input: {'key': 'value'}
  /// Example Output: 'U2FsdGVkX1+...'
  Future<String> encryptedPayload(Map<String, dynamic>? value);

  /// Decrypts the provided [encryptedData] which is a JSON string back to a [Map].
  ///
  /// This method takes an encrypted JSON string and returns the original map.
  ///
  /// Example Input: 'U2FsdGVkX1+...'
  /// Example Output: {'key': 'value'}
  Future<String> decryptedPayload(String encryptedData);
}

class AESEncryptionManagerImpl implements AESEncryptionManager {
  late Encrypter _encrypter;
  late Key _key;
  late String _base64Key;

  AESEncryptionManagerImpl() {
    _initializeEncryption();
  }

  void _initializeEncryption() {
    // Store the base64 key for MAC generation from environment variables
    _base64Key = getAuthAesKey() ?? '';
    if (_base64Key.isEmpty) {
      throw Exception('AES_KEY environment variable is not set');
    }

    // Decode the Base64 key to bytes
    final keyBytes = base64.decode(_base64Key);
    _key = Key(keyBytes);

    // Create encrypter with CBC mode and PKCS7 padding (Laravel default)
    _encrypter = Encrypter(AES(_key, mode: AESMode.cbc, padding: 'PKCS7'));
  }

  /// Generates a MAC (Message Authentication Code) for Laravel compatibility
  String _generateMac(String iv, String value) {
    // In Laravel, the MAC is generated using the concatenation of iv and value
    final concat = iv + value;

    // Laravel uses the raw key (not base64 encoded) for HMAC
    final hmac = Hmac(sha256, base64.decode(_base64Key));
    final digest = hmac.convert(utf8.encode(concat));

    // Return the hex representation of the digest
    return digest.toString();
  }

  @override
  Future<String> encryptData(String data) async {
    // Generate a random IV for each encryption
    final iv = IV.fromSecureRandom(16);
    final encrypted = _encrypter.encrypt(data, iv: iv);

    // Create Laravel compatible format (they does not accept pure ase encryption)
    final Map<String, String> payload = {
      'iv': base64.encode(iv.bytes),
      'value': encrypted.base64,
      'mac': _generateMac(base64.encode(iv.bytes), encrypted.base64),
      'tag': '',
    };

    // Encode the entire payload to base64
    return base64.encode(utf8.encode(json.encode(payload)));
  }

  @override
  Future<String> decryptData(String encryptedData) async {
    try {
      // Decode the base64 string to get the JSON payload
      final jsonStr = utf8.decode(base64.decode(encryptedData));
      final Map<String, dynamic> payload = json.decode(jsonStr);

      // Extract the IV and encrypted value
      final iv = IV.fromBase64(payload['iv']);
      final encrypted = Encrypted.fromBase64(payload['value']);

      // Decrypt the data
      return _encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      throw Exception('Failed to decrypt data: $e');
    }
  }

  @override
  Future<String> encryptedPayload(Map<String, dynamic>? value) async {
    final String encodedParams = json.encode(value);
    return await encryptData(encodedParams);
  }

  @override
  Future<String> decryptedPayload(String encryptedData) async {
    final String decryptedData = await decryptData(encryptedData);
    return decryptedData;
  }
}
