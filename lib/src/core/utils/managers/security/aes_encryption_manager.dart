import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

import '../../../constants/const.dart';

/// Enum to specify which AES key to use for encryption/decryption
enum AESKeyType {
  /// Use the authentication AES key for auth-related routes (login, signup, forgot password, etc.)
  auth,

  /// Use the translation AES key for main app features (translation, dictionary, etc.)
  translation,
}

abstract class AESEncryptionManager {
  /// Encrypts the provided [data] using AES encryption.
  ///
  /// This method takes a plain text string and returns an encrypted string.
  ///
  /// Parameters:
  /// - [data]: The plain text string to encrypt
  /// - [keyType]: The type of AES key to use (auth or translation)
  ///
  /// Example Input: 'Hello World'
  /// Example Output: 'U2FsdGVkX1+...'
  Future<String> encryptData(String data, {AESKeyType keyType = AESKeyType.auth});

  /// Decrypts the provided [encryptedData] back to its original plain text.
  ///
  /// This method takes an encrypted string and returns the original plain text string.
  ///
  /// Parameters:
  /// - [encryptedData]: The encrypted string to decrypt
  /// - [keyType]: The type of AES key to use (auth or translation)
  ///
  /// Example Input: 'U2FsdGVkX1+...'
  /// Example Output: 'Hello World'
  Future<String> decryptData(String encryptedData, {AESKeyType keyType = AESKeyType.auth});

  /// Encrypts a payload represented as a [Map] of key-value pairs.
  ///
  /// This method takes a map, converts it to a JSON string, and then encrypts it.
  ///
  /// Parameters:
  /// - [value]: The map to encrypt
  /// - [keyType]: The type of AES key to use (auth or translation)
  ///
  /// Example Input: {'key': 'value'}
  /// Example Output: 'U2FsdGVkX1+...'
  Future<String> encryptedPayload(Map<String, dynamic>? value, {AESKeyType keyType = AESKeyType.auth});

  /// Decrypts the provided [encryptedData] which is a JSON string back to a [Map].
  ///
  /// This method takes an encrypted JSON string and returns the original map.
  ///
  /// Parameters:
  /// - [encryptedData]: The encrypted JSON string to decrypt
  /// - [keyType]: The type of AES key to use (auth or translation)
  ///
  /// Example Input: 'U2FsdGVkX1+...'
  /// Example Output: {'key': 'value'}
  Future<String> decryptedPayload(String encryptedData, {AESKeyType keyType = AESKeyType.auth});
}

class AESEncryptionManagerImpl implements AESEncryptionManager {
  late Encrypter _authEncrypter;
  late Encrypter _translationEncrypter;
  late Key _authKey;
  late Key _translationKey;
  late String _authBase64Key;
  late String _translationBase64Key;

  AESEncryptionManagerImpl() {
    _initializeEncryption();
  }

  void _initializeEncryption() {
    // Initialize Auth AES key
    _authBase64Key = getAuthAesKey() ?? '';
    if (_authBase64Key.isEmpty) {
      throw Exception('AUTH_AES_KEY environment variable is not set');
    }
    final authKeyBytes = base64.decode(_authBase64Key);
    _authKey = Key(authKeyBytes);
    _authEncrypter = Encrypter(AES(_authKey, mode: AESMode.cbc, padding: 'PKCS7'));

    // Initialize Translation AES key
    _translationBase64Key = getTranslationAseKey() ?? '';
    if (_translationBase64Key.isEmpty) {
      throw Exception('TRANSLATION_AES_KEY environment variable is not set');
    }
    final translationKeyBytes = base64.decode(_translationBase64Key);
    _translationKey = Key(translationKeyBytes);
    _translationEncrypter = Encrypter(AES(_translationKey, mode: AESMode.cbc, padding: 'PKCS7'));
  }

  /// Gets the appropriate encrypter based on key type
  Encrypter _getEncrypter(AESKeyType keyType) {
    switch (keyType) {
      case AESKeyType.auth:
        return _authEncrypter;
      case AESKeyType.translation:
        return _translationEncrypter;
    }
  }

  /// Gets the appropriate base64 key based on key type
  String _getBase64Key(AESKeyType keyType) {
    switch (keyType) {
      case AESKeyType.auth:
        return _authBase64Key;
      case AESKeyType.translation:
        return _translationBase64Key;
    }
  }

  /// Generates a MAC (Message Authentication Code) for Laravel compatibility
  String _generateMac(String iv, String value, AESKeyType keyType) {
    // In Laravel, the MAC is generated using the concatenation of iv and value
    final concat = iv + value;

    // Laravel uses the raw key (not base64 encoded) for HMAC
    final hmac = Hmac(sha256, base64.decode(_getBase64Key(keyType)));
    final digest = hmac.convert(utf8.encode(concat));

    // Return the hex representation of the digest
    return digest.toString();
  }

  @override
  Future<String> encryptData(String data, {AESKeyType keyType = AESKeyType.auth}) async {
    // Generate a random IV for each encryption
    final iv = IV.fromSecureRandom(16);
    final encrypter = _getEncrypter(keyType);
    final encrypted = encrypter.encrypt(data, iv: iv);

    // Create Laravel compatible format (they does not accept pure ase encryption)
    final Map<String, String> payload = {
      'iv': base64.encode(iv.bytes),
      'value': encrypted.base64,
      'mac': _generateMac(base64.encode(iv.bytes), encrypted.base64, keyType),
      'tag': '',
    };

    // Encode the entire payload to base64
    return base64.encode(utf8.encode(json.encode(payload)));
  }

  @override
  Future<String> decryptData(String encryptedData, {AESKeyType keyType = AESKeyType.auth}) async {
    try {
      // Decode the base64 string to get the JSON payload
      final jsonStr = utf8.decode(base64.decode(encryptedData));
      final Map<String, dynamic> payload = json.decode(jsonStr);

      // Extract the IV and encrypted value
      final iv = IV.fromBase64(payload['iv']);
      final encrypted = Encrypted.fromBase64(payload['value']);

      // Decrypt the data using the appropriate encrypter
      final encrypter = _getEncrypter(keyType);
      return encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      throw Exception('Failed to decrypt data: $e');
    }
  }

  @override
  Future<String> encryptedPayload(Map<String, dynamic>? value, {AESKeyType keyType = AESKeyType.auth}) async {
    final String encodedParams = json.encode(value);
    return await encryptData(encodedParams, keyType: keyType);
  }

  @override
  Future<String> decryptedPayload(String encryptedData, {AESKeyType keyType = AESKeyType.auth}) async {
    final String decryptedData = await decryptData(encryptedData, keyType: keyType);
    return decryptedData;
  }
}
