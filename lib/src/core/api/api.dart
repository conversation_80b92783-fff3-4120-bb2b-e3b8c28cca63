import 'package:flutter_dotenv/flutter_dotenv.dart';

const String kGoogleCheckEndpoint = "https://google.com";

class Api {
  String get _baseUrl => dotenv.env['BASE_URL'] ?? "https://admin.bla.iq/api/";

  String _constructUrl(String endpoint) => "$_baseUrl$endpoint";

  // AUTH
  String get login => _constructUrl("login");
  String get logout => _constructUrl("user-logout");
  String get deleteAccount => _constructUrl("delete_user");
  String get resetPassword => _constructUrl("reset_password");
  String get forgotPassword => _constructUrl("forgot_password");
  // register
  String get checkAlreadyExist => _constructUrl("check_already_exist");
  String get registrationOtp => _constructUrl("registration_otp");
  String get verifyOtp => _constructUrl("otp_verify");
  String get signup => _constructUrl("register");

  // Translation
  String get translate => _constructUrl("Texttranslate");

  // Text to Speech
  String get textToSpeechKurdish => _constructUrl("TextToSpeech");
  String get textToSpeechOther => _constructUrl("TextToSpeechOther");

  // speech to text
  String get speechToTextKurdish => _constructUrl("SpeechToText");
  String get speechToTextOther => _constructUrl("SpeechToTextOther");

  // Document Translation
  String get docTranslate => _constructUrl("Docstranslate");

  // OCR
  String get ocrToText => _constructUrl("Ocrtotext");

  // Dictionary
  String get textToDictionary => _constructUrl("TextToDictionary");
  //contact
  String get contact => _constructUrl("contact");
}
