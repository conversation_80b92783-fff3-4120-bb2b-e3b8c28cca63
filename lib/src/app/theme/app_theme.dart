import 'package:flutter/material.dart';
import 'colors.dart';
import 'package:easy_localization/easy_localization.dart';

const double kExtraSize = 3;
const double kFontSize3XL = 28 + kExtraSize;
const double kFontSize2XL = 22 + kExtraSize;
const double kFontSizeXL = 18 + kExtraSize;
const double kFontSizeLG = 16 + kExtraSize;
const double kFontSizeM = 14 + kExtraSize;
const double kFontSizeS = 12 + kExtraSize;
const double kFontSizeXS = 11 + kExtraSize;
const double defaultTextHeight = 1.5;

class AppTheme {
  late double _textHeight;

  AppTheme() {
    _textHeight = defaultTextHeight;
  }

  String _getFontFamily(BuildContext context) {
    // Get the current locale
    final locale = context.locale;

    if (locale.languageCode == 'ar' || locale.languageCode == 'AE') {
      return 'Rabar015';
    }

    // Use Titillium for English and other languages
    return 'Titillium';
  }

  ThemeData getLightThemeData(BuildContext context) {
    final ThemeData themeData = ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: ColorPalette.primaryColor,
      primaryColorLight: ColorPalette.primaryColor,
      scaffoldBackgroundColor: ColorPalette.scaffold,
      fontFamily: _getFontFamily(context),
      canvasColor: Colors.white,
      cardColor: ColorPalette.white,
      primarySwatch: Colors.grey,
      splashColor: ColorPalette.surfaceDim,
      highlightColor: ColorPalette.surfaceDim,
      hintColor: ColorPalette.hintColor,
      dividerColor: ColorPalette.surfaceDim,
      disabledColor: ColorPalette.disabledColor,

      ///APPBAR THEME
      appBarTheme: const AppBarTheme(
        backgroundColor: ColorPalette.white,
        surfaceTintColor: Colors.transparent,
      ),
      navigationBarTheme: const NavigationBarThemeData(
        indicatorColor: ColorPalette.secondaryColor,
        backgroundColor: ColorPalette.white,
        elevation: 0,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(3)),
        ),
        shadowColor: Colors.transparent,
      ),

      /// DIVIDER THEME
      dividerTheme: const DividerThemeData(
        color: ColorPalette.surfaceDim,
      ),

      ///DIALOG THEME
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide.none,
        ),
        elevation: 0,
        actionsPadding: const EdgeInsets.all(16),
      ),

      /// INPUT DECORATION
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        errorMaxLines: 1,
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: ColorPalette.black,
          ),
        ),
        focusedErrorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: ColorPalette.red,
          ),
        ),
        errorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: ColorPalette.progressRed,
          ),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: ColorPalette.outline,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
          borderSide: BorderSide.none,
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(30)),
        errorStyle: TextStyle(
          height: _textHeight,
          fontSize: 12,
          color: ColorPalette.red,
          fontWeight: FontWeight.w500,
        ),
      ),

      ///TEXT THEME
      textTheme: getTextTheme(ColorPalette.black),

      ///COLOR SCHEME
      colorScheme: const ColorScheme.light(
        primary: ColorPalette.primaryColor,
        onPrimary: ColorPalette.white,
        secondary: ColorPalette.secondaryColor,
        onSecondary: ColorPalette.black,
        secondaryFixedDim: ColorPalette.secondaryHigh,
        tertiary: ColorPalette.tertiaryColor,
        primaryContainer: ColorPalette.primaryContainer,
        secondaryContainer: ColorPalette.secondaryContainer,
        surfaceContainerHigh: ColorPalette.tertiaryDarkest,
        surfaceContainerLow: ColorPalette.tertiaryLight,
        surface: ColorPalette.surface,
        surfaceBright: ColorPalette.white,
        surfaceDim: ColorPalette.surfaceDim,
        surfaceContainer: ColorPalette.surfaceContainer,
        error: ColorPalette.red,
        errorContainer: ColorPalette.darkRed,
        onError: ColorPalette.lightRed,
        onSurface: ColorPalette.black,
        onPrimaryContainer: ColorPalette.black,
        onSurfaceVariant: ColorPalette.textGrey,
        outline: ColorPalette.outline,
        outlineVariant: ColorPalette.outlineDK,
      ),

      ///ICON THEME
      iconTheme: const IconThemeData(
        color: ColorPalette.hintColor,
        weight: 900,
      ),

      ///BUTTON THEME
      buttonTheme: const ButtonThemeData(
        splashColor: Colors.grey,
        highlightColor: Colors.grey,
        disabledColor: ColorPalette.disabledColor,
        buttonColor: ColorPalette.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24)),
        ),
        colorScheme: ColorScheme.light(
          primary: ColorPalette.primaryColor,
          secondary: Color(0xff73F67B),
          tertiary: Color(0xffB1DFE6),
          error: ColorPalette.red,
          onSecondaryFixed: Color(0xffD2FCD5),
        ),
      ),

      ///[ELEVATED BUTTON THEME]
      elevatedButtonTheme: const ElevatedButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(EdgeInsets.symmetric(horizontal: 16, vertical: 14)),
          backgroundColor: WidgetStatePropertyAll(
            ColorPalette.primaryColor,
          ),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(48),
              ),
            ),
          ),
          elevation: WidgetStatePropertyAll(0.0),
        ),
      ),

      ///[FILLED BUTTON THEME]
      filledButtonTheme: const FilledButtonThemeData(
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          ),
          backgroundColor: WidgetStatePropertyAll(
            ColorPalette.primaryColor,
          ),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(8),
              ),
            ),
          ),
          elevation: WidgetStatePropertyAll(0.0),
        ),
      ),
    );

    return themeData;
  }

  TextTheme getTextTheme(Color color) {
    return TextTheme(
      ///
      /// ===== TITLE ======
      ///
      titleLarge: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeXL,
        fontWeight: FontWeight.w600,
        color: ColorPalette.onSurface,
        letterSpacing: 0.15,
      ),
      titleMedium: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeLG,
        fontWeight: FontWeight.w700,
        color: ColorPalette.onSurface,
        letterSpacing: 0.1,
      ),
      titleSmall: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeM,
        fontWeight: FontWeight.w400,
        color: ColorPalette.onSurface,
        letterSpacing: 0.1,
      ),

      /// ===== TITLE ======

      /// ===== BODY ======
      bodyLarge: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeLG,
        fontWeight: FontWeight.w400,
        color: ColorPalette.onSurface,
        letterSpacing: 0.15,
      ),
      bodyMedium: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeM,
        fontWeight: FontWeight.w400,
        color: ColorPalette.onSurface,
        letterSpacing: 0.25,
      ),
      bodySmall: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeS,
        overflow: TextOverflow.ellipsis,
        fontWeight: FontWeight.w400,
        color: ColorPalette.onSurface,
        letterSpacing: 0.4,
      ),

      /// ===== BODY ======

      /// ===== LABEL ======
      labelLarge: TextStyle(
        fontSize: kFontSizeM,
        fontWeight: FontWeight.w500,
        height: _textHeight,
        color: ColorPalette.onSurface,
        letterSpacing: 0.1,
      ),
      labelMedium: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeS,
        overflow: TextOverflow.ellipsis,
        color: ColorPalette.onSurface,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
      ),
      labelSmall: TextStyle(
        height: _textHeight,
        fontSize: kFontSizeS,
        color: ColorPalette.onSurface,
        overflow: TextOverflow.ellipsis,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
      ),

      /// ===== LABEL ======

      displaySmall: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: ColorPalette.textGrey,
        letterSpacing: 0.15,
      ),
      displayMedium: TextStyle(
        height: _textHeight,
        fontSize: 14,
        fontWeight: FontWeight.w700,
        color: ColorPalette.white,
        letterSpacing: 0.1,
      ),
      headlineLarge: TextStyle(
        height: _textHeight,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        overflow: TextOverflow.ellipsis,
        color: color,
        letterSpacing: 0,
      ),
      headlineMedium: TextStyle(
        height: _textHeight,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: color,
        letterSpacing: 0.25,
      ),
      headlineSmall: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: color,
        letterSpacing: 0,
      ),
    );
  }

//This can be changed according to the design
  TextTheme getTextThemeArch(Color color) {
    return TextTheme(
      /// DISPLAY TEXT
      ///
      displaySmall: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: ColorPalette.textGrey,
      ),
      displayMedium: TextStyle(
        height: _textHeight,
        fontSize: 14,
        fontWeight: FontWeight.w700,
        color: ColorPalette.white,
      ),

      ///HEADLINE TEXT (WEIGHTS ARE 600)

      /// FOR HEADLINE IN INTRO PAGES
      headlineLarge: TextStyle(
        height: _textHeight,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        overflow: TextOverflow.ellipsis,
        color: color,
      ),

      /// FOR BIG TITLES
      headlineMedium: TextStyle(
        height: _textHeight,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: color,
      ),

      /// THIS STYLE IS BEST FOR TOPIC TITLES, SETTINGS TITLES, AND PEOPLE NAMES
      headlineSmall: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: color,
      ),

      ///
      /// TITLE TEXT
      ///
      titleLarge: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w700,
        color: color,
      ),

      /// THIS STYLE IS BEST FOR LABEL TITLES IN INPUT FIELDS
      titleMedium: TextStyle(
        height: _textHeight,
        fontSize: 14,
        fontWeight: FontWeight.w700,
        color: color,
      ),

      /// THIS STYLE IS FOR SMALL TITLES LIKE NAVIGATIONBAR TITLES
      titleSmall: TextStyle(
        height: _textHeight,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: ColorPalette.hintColor,
      ),

      ///
      /// BODY TEXT (WEIGHTS ARE 400)

      /// THIS STYLE USED FOR TITLES WITH LIGHT BLACK COLOR
      bodyLarge: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: color,
      ),

      /// THIS STYLE USED FOR DESCRIPTIONS TEXTS WITH LIGHTER GREY COLOR
      bodyMedium: TextStyle(
        height: _textHeight,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: ColorPalette.hintColor,
      ),

      /// THIS STYLE USED FOR DESCRIPTIONS TEXTS UNDER TITLES
      bodySmall: TextStyle(
        height: _textHeight,
        fontSize: 14,
        overflow: TextOverflow.ellipsis,
        fontWeight: FontWeight.w400,
        color: ColorPalette.textGrey,
      ),

      ////
      /// LABEL TEXT (WEIGHTS ARE 500)
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        height: _textHeight,
        color: color,
      ),

      /// THIS STYLE USED FOR INFORMATION TEXTS UNDER TITLES
      labelMedium: TextStyle(
        height: _textHeight,
        fontSize: 12,
        overflow: TextOverflow.ellipsis,
        color: ColorPalette.textGrey,
        fontWeight: FontWeight.w500,
      ),

      /// THIS STYLE USED FOR HINT TEXT IN INPUT FIELDS
      labelSmall: TextStyle(
        height: _textHeight,
        fontSize: 12,
        color: ColorPalette.hintColor,
        overflow: TextOverflow.ellipsis,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

extension CustomTextTheme on TextTheme {
  TextStyle get titleXLarge => TextStyle(
        height: defaultTextHeight,
        fontSize: kFontSizeXL,
        fontWeight: FontWeight.w600,
        color: ColorPalette.onSurface,
        letterSpacing: 0.15,
      );
  TextStyle get bodySmallBold => TextStyle(
        height: defaultTextHeight,
        color: ColorPalette.onSurface,
        fontSize: kFontSizeS,
        fontWeight: FontWeight.w700,
        letterSpacing: 0.4,
      );
}



//  if (error.error?.code == "412") {
//   context.router.replaceAll([const TimeIsUpPageRoute()]);
// }