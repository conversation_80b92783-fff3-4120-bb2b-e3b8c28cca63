import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_framework/responsive_framework.dart';

import '../../core/common/presentation/logic/user/user_cubit.dart';
import '../../features/auth/presentation/logic/forgot_password_process/forgot_password/forgot_password_cubit.dart';
import '../../features/auth/presentation/logic/forgot_password_process/verify_otp/verify_otp_cubit.dart';
import '../../features/auth/presentation/logic/signup_process/check_exist/check_exist_cubit.dart';
import '../../features/auth/presentation/logic/signup_process/registration_otp/registration_otp_cubit.dart';
import '../../features/auth/presentation/logic/signup_process/signup/signup_cubit.dart';
import '../../features/auth/presentation/logic/delete_account/delete_account_cubit.dart';
import '../../features/document/presentation/logic/document_translation/document_translation_cubit.dart';
import '../../features/ocr/presentation/logic/ocr/ocr_cubit.dart';
import '../../features/translation/presentation/logic/translation/translation_cubit.dart';
import '../../injection.dart';
import '../routes/router.dart';
import '../theme/app_theme.dart';
import '../../features/dictionary/presentation/logic/dictionary/dictionary_cubit.dart';
import '../../features/home/<USER>/logic/contact/contact_cubit.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<UserCubit>(
          create: (context) => serviceLocator<UserCubit>(),
        ),
        BlocProvider<SignupCubit>(
          create: (context) => SignupCubit(
            repository: serviceLocator(),
          ),
        ),
        BlocProvider<CheckExistCubit>(
          create: (context) => CheckExistCubit(
            repository: serviceLocator(),
          ),
        ),
        BlocProvider<RegistrationOtpCubit>(
          create: (context) => RegistrationOtpCubit(
            repository: serviceLocator(),
          ),
        ),
        BlocProvider<VerifyOtpCubit>(
          create: (context) => VerifyOtpCubit(
            repository: serviceLocator(),
          ),
        ),
        BlocProvider<TranslationCubit>(
          create: (context) => TranslationCubit(
            repository: serviceLocator(),
          ),
        ),
        BlocProvider<DocumentTranslationCubit>(
          create: (context) => DocumentTranslationCubit(
            serviceLocator(),
          ),
        ),
        BlocProvider<OcrCubit>(
          create: (context) => serviceLocator<OcrCubit>(),
        ),
        BlocProvider<DictionaryCubit>(
          create: (context) => serviceLocator<DictionaryCubit>(),
        ),
        BlocProvider<ContactCubit>(
          create: (context) => serviceLocator<ContactCubit>(),
        ),
        BlocProvider<ForgotPasswordCubit>(
          create: (context) => serviceLocator<ForgotPasswordCubit>(),
          lazy: true,
        ),
        BlocProvider<DeleteAccountCubit>(
          create: (context) => serviceLocator<DeleteAccountCubit>(),
          lazy: true,
        ),
      ],
      child: MaterialApp.router(
        builder: (context, widget) {
          return ResponsiveScaledBox(
            width: ResponsiveValue<double>(
              context,
              conditionalValues: [
                const Condition.equals(name: MOBILE, value: 480),
                const Condition.between(start: 800, end: 1100, value: 800),
                const Condition.between(start: 480, end: 800, value: 800),
                const Condition.between(start: 1000, end: 1200, value: 1000),
              ],
            ).value,
            child: widget!,
          );
        },
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        theme: AppTheme().getLightThemeData(context),
        routerDelegate: AutoRouterDelegate(
          serviceLocator<AppRouter>(),
        ),
        routeInformationParser: serviceLocator<AppRouter>().defaultRouteParser(),
      ),
    );
  }
}
