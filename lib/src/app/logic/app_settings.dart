import 'package:flutter/cupertino.dart';

import '../../core/common/domain/entities/language.dart';
import '../../core/utils/managers/database/database_manager.dart';

class AppSettings {
  final DatabaseManager databaseManager;
  final Languages _languages = Languages();

  Language? _selectedLanguage;
  String? _xChannel;
  bool? isHcm;
  String? apiBaseUrl;

  //! add isHcm

  AppSettings({required this.databaseManager});

  //* Languages
  Languages get languages {
    return _languages;
  }

  //* selected language
  Language get selectedLanguage {
    return _selectedLanguage ??
        Language(
          id: 3,
          backendLangCode: 'en',
          shortDisplayLabel: 'En',
          fullDisplayLabel: 'English',
          local: const Locale('en', 'US'),
          fcmTopicName: 'English_Channel',
        );
  }

  //! call in the root widget
  void changeSelectedLanguage(Locale selectedLocal) {
    _selectedLanguage = _languages.languagesData.firstWhere((element) => element.local == selectedLocal);
  }

  //* theme modes data

  int? get themeID {
    return databaseManager.getData("themeID") as int?;
  }

  //* xChannel
  String get xChannel {
    return _xChannel ?? "";
  }

  set xChannel(String value) {
    _xChannel = value;
  }
}
