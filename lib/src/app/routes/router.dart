import 'package:auto_route/auto_route.dart';

import 'router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.material();

  @override
  List<AutoRoute> get routes => [
        // AutoRoute(
        //   path: '/testPage',
        //   page: TestPageRoute.page,
        //   // initial: true,
        // ),
        AutoRoute(
          path: '/splash',
          page: SplashPageRoute.page,
          initial: true,
        ),
        AutoRoute(
          path: '/login',
          page: LoginPageRoute.page,
        ),
        AutoRoute(
          path: '/forgot-password',
          page: ForgotPasswordPageRoute.page,
        ),
        AutoRoute(
          path: '/verify-otp',
          page: VerifyOTPPageRoute.page,
        ),
        AutoRoute(
          path: '/verify-registration-otp',
          page: VerifyOtpPageRoute.page,
        ),
        AutoRoute(
          path: '/common-otp-verification',
          page: CommonOtpVerificationPageRoute.page,
        ),
        AutoRoute(
          path: '/reset-password',
          page: ResetPasswordPageRoute.page,
        ),
        AutoRoute(
          path: '/home',
          page: HomePageRoute.page,
        ),
        AutoRoute(
          path: '/signup',
          page: SignupPageRoute.page,
        ),
        AutoRoute(
          path: '/about',
          page: AboutPageRoute.page,
        ),
        AutoRoute(
          path: '/how-to-use',
          page: HowToUsePageRoute.page,
        ),
        AutoRoute(
          path: '/terms-and-conditions',
          page: TermsAndConditionsPageRoute.page,
        ),
        AutoRoute(
          path: '/privacy-policy',
          page: PrivacyPolicyPageRoute.page,
        ),
        AutoRoute(
          path: '/contact',
          page: ContactPageRoute.page,
        ),
        // Registration routes removed - using SignupPage instead
        RedirectRoute(path: '*', redirectTo: '/'),
      ];
}
