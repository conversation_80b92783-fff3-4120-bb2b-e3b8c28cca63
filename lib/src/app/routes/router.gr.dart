// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i15;
import 'package:flutter/material.dart' as _i16;
import '../../../src/core/enums/auth_field_type.dart' as _i19;
import '../../../src/features/auth/data/models/payloads/forgot_password_payload.dart' as _i18;
import '../../../src/features/auth/data/models/payloads/otp_verification_data.dart' as _i17;
import '../../../src/features/auth/presentation/pages/common_otp_verification_page.dart' as _i2;
import '../../../src/features/auth/presentation/pages/forgot_password/forgot_password_page.dart' as _i4;
import '../../../src/features/auth/presentation/pages/forgot_password/reset_password_page.dart' as _i9;
import '../../../src/features/auth/presentation/pages/forgot_password/verify_otp_page.dart' as _i13;
import '../../../src/features/auth/presentation/pages/login_page.dart' as _i7;
import '../../../src/features/auth/presentation/pages/signup_page.dart' as _i10;
import '../../../src/features/auth/presentation/pages/verify_otp_page.dart' as _i14;
import '../../../src/features/home/<USER>/pages/about_page.dart' as _i1;
import '../../../src/features/home/<USER>/pages/contact_page.dart' as _i3;
import '../../../src/features/home/<USER>/pages/home_page.dart' as _i5;
import '../../../src/features/home/<USER>/pages/how_to_use_page.dart' as _i6;
import '../../../src/features/home/<USER>/pages/privacy_policy_page.dart' as _i8;
import '../../../src/features/home/<USER>/pages/terms_and_conditions_page.dart' as _i12;
import '../../../src/features/splash/presentation/pages/splash_page.dart' as _i11;

// import '../../../src/';

/// generated route for
/// [_i1.AboutPage]
class AboutPageRoute extends _i15.PageRouteInfo<void> {
  const AboutPageRoute({List<_i15.PageRouteInfo>? children}) : super(AboutPageRoute.name, initialChildren: children);

  static const String name = 'AboutPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i1.AboutPage();
    },
  );
}

/// generated route for
/// [_i2.CommonOtpVerificationPage]
class CommonOtpVerificationPageRoute extends _i15.PageRouteInfo<CommonOtpVerificationPageRouteArgs> {
  CommonOtpVerificationPageRoute({
    _i16.Key? key,
    required _i17.OtpVerificationData data,
    List<_i15.PageRouteInfo>? children,
  }) : super(
          CommonOtpVerificationPageRoute.name,
          args: CommonOtpVerificationPageRouteArgs(key: key, data: data),
          initialChildren: children,
        );

  static const String name = 'CommonOtpVerificationPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CommonOtpVerificationPageRouteArgs>();
      return _i2.CommonOtpVerificationPage(key: args.key, data: args.data);
    },
  );
}

class CommonOtpVerificationPageRouteArgs {
  const CommonOtpVerificationPageRouteArgs({this.key, required this.data});

  final _i16.Key? key;

  final _i17.OtpVerificationData data;

  @override
  String toString() {
    return 'CommonOtpVerificationPageRouteArgs{key: $key, data: $data}';
  }
}

/// generated route for
/// [_i3.ContactPage]
class ContactPageRoute extends _i15.PageRouteInfo<void> {
  const ContactPageRoute({List<_i15.PageRouteInfo>? children}) : super(ContactPageRoute.name, initialChildren: children);

  static const String name = 'ContactPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i3.ContactPage();
    },
  );
}

/// generated route for
/// [_i4.ForgotPasswordPage]
class ForgotPasswordPageRoute extends _i15.PageRouteInfo<void> {
  const ForgotPasswordPageRoute({List<_i15.PageRouteInfo>? children}) : super(ForgotPasswordPageRoute.name, initialChildren: children);

  static const String name = 'ForgotPasswordPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i4.ForgotPasswordPage();
    },
  );
}

/// generated route for
/// [_i5.HomePage]
class HomePageRoute extends _i15.PageRouteInfo<void> {
  const HomePageRoute({List<_i15.PageRouteInfo>? children}) : super(HomePageRoute.name, initialChildren: children);

  static const String name = 'HomePageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i5.HomePage();
    },
  );
}

/// generated route for
/// [_i6.HowToUsePage]
class HowToUsePageRoute extends _i15.PageRouteInfo<void> {
  const HowToUsePageRoute({List<_i15.PageRouteInfo>? children}) : super(HowToUsePageRoute.name, initialChildren: children);

  static const String name = 'HowToUsePageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i6.HowToUsePage();
    },
  );
}

/// generated route for
/// [_i7.LoginPage]
class LoginPageRoute extends _i15.PageRouteInfo<void> {
  const LoginPageRoute({List<_i15.PageRouteInfo>? children}) : super(LoginPageRoute.name, initialChildren: children);

  static const String name = 'LoginPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i7.LoginPage();
    },
  );
}

/// generated route for
/// [_i8.PrivacyPolicyPage]
class PrivacyPolicyPageRoute extends _i15.PageRouteInfo<void> {
  const PrivacyPolicyPageRoute({List<_i15.PageRouteInfo>? children}) : super(PrivacyPolicyPageRoute.name, initialChildren: children);

  static const String name = 'PrivacyPolicyPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i8.PrivacyPolicyPage();
    },
  );
}

/// generated route for
/// [_i9.ResetPasswordPage]
class ResetPasswordPageRoute extends _i15.PageRouteInfo<ResetPasswordPageRouteArgs> {
  ResetPasswordPageRoute({
    _i16.Key? key,
    required _i18.ForgotPasswordPayload payload,
    List<_i15.PageRouteInfo>? children,
  }) : super(
          ResetPasswordPageRoute.name,
          args: ResetPasswordPageRouteArgs(key: key, payload: payload),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordPageRouteArgs>();
      return _i9.ResetPasswordPage(key: args.key, payload: args.payload);
    },
  );
}

class ResetPasswordPageRouteArgs {
  const ResetPasswordPageRouteArgs({this.key, required this.payload});

  final _i16.Key? key;

  final _i18.ForgotPasswordPayload payload;

  @override
  String toString() {
    return 'ResetPasswordPageRouteArgs{key: $key, payload: $payload}';
  }
}

/// generated route for
/// [_i10.SignupPage]
class SignupPageRoute extends _i15.PageRouteInfo<void> {
  const SignupPageRoute({List<_i15.PageRouteInfo>? children}) : super(SignupPageRoute.name, initialChildren: children);

  static const String name = 'SignupPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i10.SignupPage();
    },
  );
}

/// generated route for
/// [_i11.SplashPage]
class SplashPageRoute extends _i15.PageRouteInfo<void> {
  const SplashPageRoute({List<_i15.PageRouteInfo>? children}) : super(SplashPageRoute.name, initialChildren: children);

  static const String name = 'SplashPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i11.SplashPage();
    },
  );
}

/// generated route for
/// [_i12.TermsAndConditionsPage]
class TermsAndConditionsPageRoute extends _i15.PageRouteInfo<void> {
  const TermsAndConditionsPageRoute({List<_i15.PageRouteInfo>? children}) : super(TermsAndConditionsPageRoute.name, initialChildren: children);

  static const String name = 'TermsAndConditionsPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      return const _i12.TermsAndConditionsPage();
    },
  );
}

/// generated route for
/// [_i13.VerifyOTPPage]
class VerifyOTPPageRoute extends _i15.PageRouteInfo<VerifyOTPPageRouteArgs> {
  VerifyOTPPageRoute({
    _i16.Key? key,
    required _i18.ForgotPasswordPayload payload,
    List<_i15.PageRouteInfo>? children,
  }) : super(
          VerifyOTPPageRoute.name,
          args: VerifyOTPPageRouteArgs(key: key, payload: payload),
          initialChildren: children,
        );

  static const String name = 'VerifyOTPPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<VerifyOTPPageRouteArgs>();
      return _i13.VerifyOTPPage(key: args.key, payload: args.payload);
    },
  );
}

class VerifyOTPPageRouteArgs {
  const VerifyOTPPageRouteArgs({this.key, required this.payload});

  final _i16.Key? key;

  final _i18.ForgotPasswordPayload payload;

  @override
  String toString() {
    return 'VerifyOTPPageRouteArgs{key: $key, payload: $payload}';
  }
}

/// generated route for
/// [_i14.VerifyOtpPage]
class VerifyOtpPageRoute extends _i15.PageRouteInfo<VerifyOtpPageRouteArgs> {
  VerifyOtpPageRoute({
    _i16.Key? key,
    required String email,
    required String name,
    required String password,
    required String token,
    required _i19.AuthFieldType type,
    List<_i15.PageRouteInfo>? children,
  }) : super(
          VerifyOtpPageRoute.name,
          args: VerifyOtpPageRouteArgs(
            key: key,
            email: email,
            name: name,
            password: password,
            token: token,
            type: type,
          ),
          initialChildren: children,
        );

  static const String name = 'VerifyOtpPageRoute';

  static _i15.PageInfo page = _i15.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<VerifyOtpPageRouteArgs>();
      return _i14.VerifyOtpPage(
        key: args.key,
        email: args.email,
        name: args.name,
        password: args.password,
        token: args.token,
        type: args.type,
      );
    },
  );
}

class VerifyOtpPageRouteArgs {
  const VerifyOtpPageRouteArgs({
    this.key,
    required this.email,
    required this.name,
    required this.password,
    required this.token,
    required this.type,
  });

  final _i16.Key? key;

  final String email;

  final String name;

  final String password;

  final String token;

  final _i19.AuthFieldType type;

  @override
  String toString() {
    return 'VerifyOtpPageRouteArgs{key: $key, email: $email, name: $name, password: $password, token: $token, type: $type}';
  }
}
