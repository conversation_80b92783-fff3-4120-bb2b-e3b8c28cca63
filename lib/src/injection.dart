import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'app/logic/app_settings.dart';
import 'app/routes/router.dart';
import 'core/common/presentation/logic/user/user_cubit.dart';
import 'core/utils/helpers/gms_hms_checker.dart';
import 'core/utils/managers/analytics/firebase_analytics_manager.dart';
import 'core/utils/managers/analytics/firebase_crash_analytic_manager.dart';
import 'core/utils/managers/database/database_manager.dart';
import 'core/utils/managers/firebase_services_engine/services_engine.dart';
import 'core/utils/managers/http/check_endpoint_reachability.dart';
import 'core/utils/managers/http/http_manager.dart';
import 'core/utils/managers/notification/gms_notification_manager.dart';
import 'core/utils/managers/notification/notification_message_handler.dart';
import 'core/utils/managers/security/aes_encryption_manager.dart';
// RecaptchaService removed

import 'features/auth/data/data_sources/remote/auth_remote_datasources.dart';
import 'features/auth/data/repositories/user_repositories_impl.dart';
import 'features/auth/domain/repositories/auth_repositories.dart';
import 'features/auth/presentation/logic/forgot_password_process/forgot_password/forgot_password_cubit.dart';
import 'features/auth/presentation/logic/login/login_cubit.dart';
import 'features/auth/presentation/logic/forgot_password_process/reset_password/reset_password_cubit.dart';
import 'features/auth/presentation/logic/forgot_password_process/verify_otp/verify_otp_cubit.dart';
import 'features/auth/presentation/logic/delete_account/delete_account_cubit.dart';
import 'features/auth/presentation/logic/signup_process/check_exist/check_exist_cubit.dart';
import 'features/auth/presentation/logic/signup_process/registration_otp/registration_otp_cubit.dart';
import 'features/auth/presentation/logic/signup_process/signup/signup_cubit.dart';
import 'features/document/domain/repositories/document_translation_repository.dart';
import 'features/translation/data/datasources/remote/translation_remote_datasource.dart';
import 'features/translation/data/repositories/translation_repository_impl.dart';
import 'features/translation/domain/repositories/translation_repository.dart';
import 'features/translation/presentation/logic/translation/translation_cubit.dart';
import 'features/translation/data/datasources/remote/text_to_speech_remote_datasource.dart';
import 'features/translation/data/repositories/text_to_speech_repository_impl.dart';
import 'features/translation/domain/repositories/text_to_speech_repository.dart';
import 'features/translation/presentation/logic/text_to_speech/text_to_speech_cubit.dart';
import 'features/splash/data/datasources/local/first_time_launched_data_source.dart';
import 'features/splash/data/datasources/remote/app_status_data_source.dart';
import 'features/splash/data/repositories/app_status_repository_impl.dart';
import 'features/splash/data/repositories/check_first_launch_repository_impl.dart';
import 'features/splash/domain/repositories/app_status_repository.dart';
import 'features/splash/domain/repositories/check_first_launch_repository.dart';
import 'features/splash/domain/usecases/check_first_launch.dart';
import 'features/splash/domain/usecases/get_app_status.dart';
import 'features/document/data/datasources/remote/document_translation_remote_datasource.dart';
import 'features/document/data/repositories/document_translation_repository_impl.dart';
import 'features/document/presentation/logic/document_translation/document_translation_cubit.dart';
import 'features/ocr/data/datasources/remote/ocr_remote_datasource.dart';
import 'features/ocr/data/repositories/ocr_repository_impl.dart';
import 'features/ocr/domain/repositories/ocr_repository.dart';
import 'features/ocr/presentation/logic/ocr/ocr_cubit.dart';
import 'features/dictionary/data/datasources/remote/dictionary_remote_data_source.dart';
import 'features/dictionary/data/repositories/dictionary_repository_impl.dart';
import 'features/dictionary/domain/repositories/dictionary_repository.dart';
import 'features/dictionary/presentation/logic/dictionary/dictionary_cubit.dart';
import 'features/home/<USER>/datasources/remote/contact_remote_datasource.dart';
import 'features/home/<USER>/repositories/contact_repository_impl.dart';
import 'features/home/<USER>/repositories/contact_repository.dart';
import 'features/home/<USER>/logic/contact/contact_cubit.dart';
import 'features/translation/data/datasources/remote/speech_to_text_remote_datasource.dart';
import 'features/translation/data/repositories/speech_to_text_repository_impl.dart';
import 'features/translation/domain/repositories/speech_to_text_repository.dart';
import 'features/translation/presentation/logic/speech_to_text/speech_to_text_cubit.dart';

final serviceLocator = GetIt.instance;

Future<void> init() async {
  initInjections(serviceLocator);
  serviceLocator.allowReassignment = true;

  // Register document translation dependencies
  serviceLocator.registerLazySingleton<DocumentTranslationRemoteDataSource>(
    () => DocumentTranslationRemoteDataSourceImpl(
      httpManager: serviceLocator(),
      aesEncryptionManager: serviceLocator(),
    ),
  );

  serviceLocator.registerLazySingleton<DocumentTranslationRepository>(
    () => DocumentTranslationRepositoryImpl(serviceLocator()),
  );

  serviceLocator.registerFactory(
    () => DocumentTranslationCubit(serviceLocator()),
  );

  // Register OCR dependencies
  serviceLocator.registerLazySingleton<OcrRemoteDataSource>(
    () => OcrRemoteDataSourceImpl(
      httpManager: serviceLocator(),
      aesEncryptionManager: serviceLocator(),
    ),
  );

  serviceLocator.registerLazySingleton<OcrRepository>(
    () => OcrRepositoryImpl(serviceLocator()),
  );

  serviceLocator.registerFactory(
    () => OcrCubit(serviceLocator()),
  );

  // Register Dictionary dependencies
  serviceLocator.registerLazySingleton<IDictionaryRemoteDataSource>(
    () => DictionaryRemoteDataSource(
      httpManager: serviceLocator(),
      aesEncryptionManager: serviceLocator(),
    ),
  );

  serviceLocator.registerLazySingleton<DictionaryRepository>(
    () => DictionaryRepositoryImpl(
      remoteDataSource: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory(
    () => DictionaryCubit(serviceLocator()),
  );

  // Contact Dependencies
  serviceLocator.registerFactory<ContactRemoteDataSource>(
    () => ContactRemoteDataSourceImpl(
        httpManager: serviceLocator(), aesEncryptionManager: serviceLocator()),
  );

  serviceLocator.registerFactory<ContactRepository>(
    () => ContactRepositoryImpl(remoteDataSource: serviceLocator()),
  );

  serviceLocator.registerFactory<ContactCubit>(
    () => ContactCubit(repository: serviceLocator()),
  );

  // Register Speech to Text dependencies
  serviceLocator.registerLazySingleton<SpeechToTextRemoteDataSource>(
    () => SpeechToTextRemoteDataSourceImpl(
        aesEncryptionManager: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<SpeechToTextRepository>(
    () => SpeechToTextRepositoryImpl(remoteDataSource: serviceLocator()),
  );

  serviceLocator.registerFactory(
    () => SpeechToTextCubit(repository: serviceLocator()),
  );
}

void initInjections(GetIt serviceLocator) {
  // Utils

  serviceLocator.registerLazySingleton<CheckEndpointReachability>(
    () => CheckEndpointReachabilityImpl(),
  );

  serviceLocator.registerLazySingleton<AppSettings>(
    () => AppSettings(databaseManager: serviceLocator()),
  );

  //* permission manager

  serviceLocator.registerFactory<GmsAndHmsChecker>(
    () => GmsAndHmsChecker(),
  );

  //! App

  //* Logic

  //* Router
  serviceLocator.registerLazySingleton<AppRouter>(
    () => AppRouter(),
  );

  //! core

  //* Database
  serviceLocator.registerLazySingleton<DatabaseManager>(
    () => DatabaseManagerImpl(),
  );

  //* Security
  serviceLocator.registerLazySingleton<AESEncryptionManager>(
    () => AESEncryptionManagerImpl(),
  );

  // RecaptchaService removed - using WebView implementation instead

  //* Network
  serviceLocator.registerLazySingleton<BaseOptions>(
    () => BaseOptions(
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        "charset": "utf-8",
        "Accept-Charset": "utf-8",
      },
      responseType: ResponseType.plain,
      receiveDataWhenStatusError: true,
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 15),
      sendTimeout: const Duration(seconds: 15),
    ),
  );

  serviceLocator.registerLazySingleton<HttpManager>(
    () => HttpManagerImpl(
      baseOptions: serviceLocator(),
      databaseManager: serviceLocator(),
    ),
  );

  //* Analytics service
  serviceLocator.registerLazySingleton<FirebaseAnalyticsManager>(
    () => FirebaseAnalyticsManagerImpl(),
  );
  serviceLocator.registerLazySingleton<FirebaseCrashAnalyticManager>(
    () => FirebaseCrashAnalyticManagerImpl(),
  );

  //* Push notification
  serviceLocator.registerLazySingleton<NotificationMessageHandler>(
    () => NotificationMessageHandlerImpl(),
  );

  serviceLocator.registerLazySingleton<GmsNotificationsManager>(
    () => GmsNotificationsManager(
      notificationMessageHandler: serviceLocator(),
    ),
  );

  serviceLocator.registerLazySingleton<ServicesEngine>(
    () => ServicesEngineImpl(
      gmsAndHmsChecker: serviceLocator(),
      httpManager: serviceLocator(),
      firebaseAnalyticsManager: serviceLocator(),
      firebaseCrashAnalyticManager: serviceLocator(),
    ),
  );

  //* data sources
  serviceLocator.registerFactory<AppStatusDataSource>(
    () => AppStatusDataSourceImpl(
      httpManager: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<FirstTimeAppLaunchedDataSource>(
    () => FirstTimeAppLaunchedDataSourceImpl(
      databaseManager: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(serviceLocator(), serviceLocator()),
  );

  serviceLocator.registerFactory<TranslationRemoteDataSource>(
    () => TranslationRemoteDataSourceImpl(
      httpManager: serviceLocator(),
      aesEncryptionManager: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<TextToSpeechRemoteDataSource>(
    () => TextToSpeechRemoteDataSourceImpl(
      httpManager: serviceLocator(),
      aesEncryptionManager: serviceLocator(),
    ),
  );

  //* repositories
  serviceLocator.registerFactory<AppStatusRepository>(
    () => AppStatusRepositoryImpl(
      appStatusDataSource: serviceLocator(),
      gmsAndHmsChecker: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<CheckFirstLaunchRepository>(
    () => CheckFirstLaunchRepositoryImpl(
      firstTimeAppLaunchedDataSource: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<AuthRepository>(
    () => UserRepositoryImpl(
      authRemoteDataSource: serviceLocator(),
      databaseManager: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<TranslationRepository>(
    () => TranslationRepositoryImpl(
      translationRemoteDataSource: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<TextToSpeechRepository>(
    () => TextToSpeechRepositoryImpl(
      textToSpeechRemoteDataSource: serviceLocator(),
    ),
  );

  //* use cases
  serviceLocator.registerFactory<GetAppStatus>(
    () => GetAppStatus(repository: serviceLocator()),
  );
  serviceLocator.registerFactory<CheckFirstLaunch>(
    () => CheckFirstLaunch(repository: serviceLocator()),
  );

  //* bloc/cubit
  serviceLocator.registerFactory<LoginCubit>(
    () => LoginCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<ForgotPasswordCubit>(
    () => ForgotPasswordCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<VerifyOtpCubit>(
    () => VerifyOtpCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<ResetPasswordCubit>(
    () => ResetPasswordCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<DeleteAccountCubit>(
    () => DeleteAccountCubit(authRepository: serviceLocator()),
  );

  serviceLocator.registerFactory<UserCubit>(
    () => UserCubit(),
  );

  serviceLocator.registerFactory<TranslationCubit>(
    () => TranslationCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<TextToSpeechCubit>(
    () => TextToSpeechCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<CheckExistCubit>(
    () => CheckExistCubit(repository: serviceLocator()),
  );

  serviceLocator.registerFactory<RegistrationOtpCubit>(
    () => RegistrationOtpCubit(
      repository: serviceLocator(),
    ),
  );

  serviceLocator.registerFactory<SignupCubit>(
    () => SignupCubit(repository: serviceLocator()),
  );
}
